# Image Slider Widget for Shopify Atelier Theme v2.1.6

A comprehensive, mobile-responsive image slider widget designed specifically for contact us pages and other sections in your Shopify store. This widget features smooth scrolling animations, extensive customization options, and follows Shopify best practices.

## Features

### Core Functionality
- **Smooth Scrolling**: Hardware-accelerated CSS transitions with cubic-bezier easing
- **Mobile Responsive**: Optimized for all device sizes with touch/swipe support
- **Auto-play**: Configurable auto-advance with pause on hover and visibility detection
- **Navigation**: Arrow navigation and pagination dots
- **Multiple Slides Per View**: Show 1-4 slides on desktop, 1-2 on mobile

### Customization Options
- **Slider Speed**: Adjustable auto-play timing (2-10 seconds)
- **Image Border Radius**: 0-50px customizable rounded corners
- **Slide Dimensions**: Configurable height for desktop and mobile
- **Gap Between Slides**: Adjustable spacing between slides
- **Content Overlay**: Customizable opacity for text overlays
- **Color Scheme**: Full color customization for all elements

### Performance Features
- **Intersection Observer**: Only starts auto-play when visible
- **Hardware Acceleration**: GPU-accelerated animations
- **Lazy Loading**: Images load only when needed
- **Responsive Images**: Multiple image sizes with srcset
- **Reduced Motion Support**: Respects user accessibility preferences

## Installation

1. **Upload the Section File**
   - Go to Shopify Admin → Online Store → Themes → Actions → Edit code
   - In the "Sections" folder, click "Add a new section"
   - Name it `image-slider-widget.liquid`
   - Copy and paste the content from `sections/image-slider-widget.liquid`

2. **Add to Your Template**
   - In the theme editor, navigate to the page where you want the slider
   - Click "Add section" and select "Image Slider Widget"
   - Or add `{% section 'image-slider-widget' %}` directly to your template

## Configuration

### Layout Settings
- **Slider Height**: Set different heights for desktop (200-800px) and mobile (150-500px)
- **Slides Per View**: Display 1-4 slides simultaneously on desktop, 1-2 on mobile
- **Gap Between Slides**: Adjust spacing between slides (0-50px)
- **Section Padding**: Control top and bottom padding (0-200px)

### Slider Behavior
- **Auto Play**: Enable/disable automatic slide advancement
- **Auto Play Speed**: Set timing between slides (2-10 seconds)
- **Show Navigation**: Toggle arrow navigation buttons
- **Show Pagination**: Toggle pagination dots

### Image Styling
- **Border Radius**: Customize image corner rounding (0-50px)
- **Content Overlay Opacity**: Adjust text overlay transparency (0-100%)

### Color Customization
- **Section Background**: Set the overall section background color
- **Navigation Colors**: Customize arrow colors and backgrounds
- **Pagination Colors**: Set dot colors for inactive and active states
- **Content Text Color**: Choose text color for slide titles and descriptions

## Adding Slides

1. In the theme editor, click "Add block" in the Image Slider Widget section
2. Select "Slide" to add a new slide
3. Configure each slide:
   - **Slide Image**: Upload your image (recommended: 1200x900px)
   - **Image Alt Text**: Add descriptive text for accessibility
   - **Slide Title**: Optional title that appears on hover
   - **Slide Description**: Optional description text
   - **Slide Link**: Optional URL to link when slide is clicked

## Best Practices

### Image Optimization
- **Recommended Size**: 1200x900px for optimal quality
- **File Format**: Use WebP when possible, fallback to JPG
- **File Size**: Keep images under 500KB for best performance
- **Alt Text**: Always provide descriptive alt text for accessibility

### Content Guidelines
- **Title Length**: Keep titles under 50 characters
- **Description Length**: Limit descriptions to 2-3 lines
- **Contrast**: Ensure good contrast between text and background images

### Performance Tips
- **Limit Slides**: Use 3-8 slides for optimal performance
- **Auto-play Speed**: Don't set too fast (minimum 3 seconds recommended)
- **Mobile Optimization**: Test on actual devices for best experience

## Accessibility Features

- **Keyboard Navigation**: Full keyboard support for all controls
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Reduced Motion**: Respects `prefers-reduced-motion` setting
- **Focus Indicators**: Clear focus states for all interactive elements
- **Alt Text Support**: Comprehensive image alt text support

## Browser Support

- **Modern Browsers**: Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **Mobile Browsers**: iOS Safari 12+, Chrome Mobile 60+
- **Fallbacks**: Graceful degradation for older browsers

## Troubleshooting

### Images Not Displaying
- Check that images are properly uploaded in the theme editor
- Verify image URLs are accessible
- Ensure images are not too large (max 20MB)

### Slider Not Working
- Check browser console for JavaScript errors
- Verify the section is properly added to the template
- Ensure no conflicting CSS or JavaScript

### Performance Issues
- Reduce image file sizes
- Limit number of slides
- Check for conflicting theme scripts

## Technical Details

### File Structure
```
sections/image-slider-widget.liquid  # Complete widget file
├── HTML Structure
├── CSS Styling (inline)
├── JavaScript Functionality (inline)
└── Shopify Schema Configuration
```

### Dependencies
- No external dependencies required
- Uses native browser APIs (Intersection Observer, Touch Events)
- Compatible with Shopify's theme editor

### Performance Metrics
- **First Paint**: Optimized for fast initial render
- **Interaction Ready**: Touch/click responsive within 100ms
- **Memory Usage**: Minimal DOM manipulation and event cleanup
- **Animation Performance**: 60fps smooth animations

## Support

For issues or customization requests, refer to the troubleshooting section above or consult the Shopify theme documentation.

## Version History

- **v1.0**: Initial release with core functionality
- **v1.1**: Added touch/swipe support and performance optimizations
- **v1.2**: Enhanced accessibility and mobile responsiveness
