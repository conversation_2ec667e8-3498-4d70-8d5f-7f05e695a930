# Enhanced Custom Footer Widget - Customization Guide

I've added extensive customization options to your custom footer widget for Atelier Theme v2.1.6. This guide explains all the new features and how to use them effectively.

## New Customization Categories

### 1. Layout Settings

| Setting | Description | Options |
|---------|-------------|---------|
| **Footer Width** | Controls the overall width of the footer | Full Width / Container Width |
| **Top Padding** | Adjusts space above footer content | 0-100px (default: 60px) |
| **Bottom Padding** | Adjusts space below footer content | 0-100px (default: 20px) |

### 2. Typography Settings

| Setting | Description | Options |
|---------|-------------|---------|
| **Heading Size** | Controls column title font size | Small / Medium / Large |
| **Text Size** | Controls menu item text size | Small / Medium / Large |

### 3. Color Settings

| Setting | Description | Default |
|---------|-------------|---------|
| **Heading Color** | Color for all column titles | #222 |
| **Text Color** | Color for standard text | #555 |
| **Link Color** | Color for menu links | #555 |
| **Link Hover Color** | Color when links are hovered | #000 |

### 4. Border Styling

| Setting | Description | Default |
|---------|-------------|---------|
| **Show Top Border** | Toggle border at top of footer | Enabled |
| **Border Color** | Color of the top border | #e8e8e8 |
| **Border Width** | Thickness of the top border | 1-5px |

### 5. Bottom Bar Styling

| Setting | Description | Default |
|---------|-------------|---------|
| **Bottom Background** | Background color of bottom bar | rgba(0,0,0,0.03) |

### 6. Background Options

| Setting | Description | Default |
|---------|-------------|---------|
| **Background Image** | Footer background image | None |
| **Background Color** | Background or overlay color | #f9f8f6 |
| **Overlay Opacity** | Transparency of background overlay | 85% |

## Mobile Responsiveness

The footer is now fully responsive with the following breakpoints:

| Screen Size | Description |
|-------------|-------------|
| **>1200px** | Full desktop layout with 5 columns |
| **991-1200px** | 4 columns per row |
| **767-991px** | 3 columns per row |
| **480-767px** | 2 columns per row with accordion dropdowns |
| **<480px** | Single column with accordion dropdowns |

## Atelier Theme Integration

This custom footer has been built specifically for Atelier Theme v2.1.6, following the theme's design patterns:

1. **CSS Variables** - Uses Atelier theme CSS variables for consistent styling
2. **Typography** - Respects the theme's typography system
3. **Section Structure** - Follows the theme's section organization pattern
4. **Colors** - Integrates with the theme's color scheme

## Best Practices for Customization

1. **Color Scheme** - Match your footer colors to your overall theme design
2. **Typography** - Use consistent text sizes (medium is recommended for most sites)
3. **Background** - For wooden backgrounds like in your reference image:
   - Set overlay opacity to 50-60%
   - Choose a light overlay color (#f9f8f6)
4. **Mobile Experience** - Test on all device sizes after making changes

## Tips for Specific Layouts

### For Full-Width Wooden Background (Like Your Reference)
- Set Footer Width to "Full Width"
- Upload a wooden deck image (2000 x 800px)
- Set Overlay Opacity to around 50%
- Set Top Padding to 60px
- Set Bottom Padding to 0px

### For Cleaner Modern Look
- Set Footer Width to "Container Width"
- No background image
- Use a light grey background color
- Show top border enabled
- Use medium heading and text sizes

## Additional Modifications

If you need further customization beyond what the settings provide, you can directly edit:

1. `assets/custom-footer.css` - For styling changes
2. `assets/custom-footer.js` - For behavior changes
3. `sections/footer-custom.liquid` - For structural changes

Or use the all-in-one version which has everything included in one file.

Enjoy your highly customizable footer!
