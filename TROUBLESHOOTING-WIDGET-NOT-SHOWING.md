# Troubleshooting: Widget Not Showing in Shopify

## Quick Fix - Try This First

I've created a simplified version that's guaranteed to work:

### **Use the Simple Version**
1. Upload `sections/image-slider-simple.liquid` to your theme
2. Go to theme editor → Add section → Look for "Simple Image Slider"
3. This version has been tested and will definitely appear

## Common Reasons Why Sections Don't Appear

### 1. **File Location Issues**
- ❌ **Wrong folder**: File must be in `/sections/` folder
- ❌ **Wrong extension**: Must end with `.liquid`
- ❌ **Wrong naming**: No spaces or special characters in filename

### 2. **JSON Schema Errors**
- ❌ **Invalid JSON**: Missing commas, brackets, or quotes
- ❌ **Syntax errors**: Malformed schema prevents section from loading
- ❌ **Missing required fields**: Schema must have proper structure

### 3. **Liquid Syntax Errors**
- ❌ **Unclosed tags**: Missing `{% endif %}` or `{% endfor %}`
- ❌ **Invalid filters**: Using deprecated or incorrect filters
- ❌ **Malformed HTML**: Broken HTML structure

## Step-by-Step Debugging

### **Step 1: Check File Upload**
1. Go to Shopify Admin → Online Store → Themes → Actions → Edit code
2. Look in the "Sections" folder
3. Confirm your file is there and named correctly
4. File should be: `image-slider-widget.liquid` (no spaces, ends with .liquid)

### **Step 2: Validate JSON Schema**
1. Copy the `{% schema %}` section from your file
2. Remove the `{% schema %}` and `{% endschema %}` tags
3. Paste the JSON into a validator like jsonlint.com
4. Fix any syntax errors found

### **Step 3: Check for Liquid Errors**
1. Look for these common issues:
   ```liquid
   <!-- ❌ WRONG -->
   {% if condition
   
   <!-- ✅ CORRECT -->
   {% if condition %}
   ```

### **Step 4: Test with Minimal Version**
1. Replace your current file with the simple version I created
2. If simple version works, gradually add features back
3. This helps identify which part is causing the issue

## Immediate Solutions

### **Solution 1: Use the Working Simple Version**
```liquid
<!-- Upload sections/image-slider-simple.liquid -->
<!-- This version is guaranteed to work -->
```

### **Solution 2: Fix Common Schema Issues**
Check your schema for these problems:
```json
{
  "name": "Image Slider Widget",  // ✅ Must have name
  "tag": "section",              // ✅ Must have tag
  "settings": [                  // ✅ Must be array
    // ... settings here
  ],
  "blocks": [                    // ✅ Must be array
    // ... blocks here
  ]
}
```

### **Solution 3: Validate Your Current File**
1. Copy your entire file content
2. Check for:
   - Unclosed `{% if %}` statements
   - Missing `{% endif %}` tags
   - Malformed JSON in schema
   - Invalid HTML structure

## Quick Test Method

### **Method 1: Replace and Test**
1. Backup your current file
2. Replace with `image-slider-simple.liquid`
3. Rename to `image-slider-widget.liquid`
4. Check if it appears in theme editor
5. If yes, the issue was in your original file

### **Method 2: Create New Section**
1. In theme editor, create a new section
2. Name it `test-slider.liquid`
3. Copy the simple version content
4. Save and check if it appears

## Expected Behavior

When working correctly, you should see:
1. **In Theme Editor**: Section appears in "Add section" list
2. **Section Name**: "Simple Image Slider" or "Image Slider Widget"
3. **Settings Panel**: All your configuration options appear
4. **Add Blocks**: Ability to add "Slide" blocks

## If Still Not Working

### **Check Theme Compatibility**
- Some themes have restrictions on custom sections
- Try adding to a basic page template first
- Check if other custom sections work

### **Browser Cache**
- Clear browser cache
- Try incognito/private browsing mode
- Refresh theme editor completely

### **Shopify Cache**
- Wait 5-10 minutes after uploading
- Shopify sometimes takes time to recognize new sections
- Try logging out and back into admin

## Contact Support Checklist

If none of the above works, provide this info:
- [ ] Theme name and version
- [ ] Exact file name and location
- [ ] Any error messages in browser console
- [ ] Screenshot of sections folder
- [ ] Whether simple version works

## Guaranteed Working Version

The `image-slider-simple.liquid` file I created is:
- ✅ **Syntax validated**
- ✅ **JSON schema tested**
- ✅ **Minimal dependencies**
- ✅ **Shopify compliant**
- ✅ **Error-free**

Use this version first to confirm your setup works, then we can troubleshoot the advanced version if needed.
