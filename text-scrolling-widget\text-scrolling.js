/**
 * Text Scrolling Widget JavaScript
 * Compatible with Atelier Theme v2.1.6
 * Handles scroll-triggered animations and dynamic behavior
 */

class TextScrollingWidget {
  constructor(element) {
    this.widget = element;
    this.track = element.querySelector('.text-scrolling-track');
    this.container = element.querySelector('.text-scrolling-container');
    this.items = element.querySelectorAll('.text-scrolling-item');
    
    this.scrollDirection = element.dataset.scrollDirection || 'left-to-right';
    this.scrollSpeed = element.dataset.scrollSpeed || '20';
    this.animationTrigger = element.dataset.animationTrigger || 'on-scroll';
    
    this.isVisible = false;
    this.isAnimating = false;
    
    this.init();
  }
  
  init() {
    this.setupAnimation();
    this.setupIntersectionObserver();
    this.setupEventListeners();
    this.duplicateItems();
    
    // Add loading class initially
    this.widget.classList.add('loading');
    
    // Remove loading class after a short delay
    setTimeout(() => {
      this.widget.classList.remove('loading');
      this.widget.classList.add('loaded');
    }, 100);
  }
  
  setupAnimation() {
    // Set animation class based on direction
    const animationClass = this.scrollDirection === 'left-to-right' 
      ? 'animate-left-to-right' 
      : 'animate-right-to-left';
    
    this.track.classList.add(animationClass);
    
    // Handle different trigger types
    if (this.animationTrigger === 'on-load') {
      this.startAnimation();
    } else if (this.animationTrigger === 'on-hover') {
      this.track.classList.add('paused');
    } else {
      // on-scroll - will be handled by intersection observer
      this.track.classList.add('paused');
    }
  }
  
  setupIntersectionObserver() {
    if (this.animationTrigger !== 'on-scroll') return;
    
    const options = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };
    
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !this.isVisible) {
          this.isVisible = true;
          this.startAnimation();
        } else if (!entry.isIntersecting && this.isVisible) {
          this.isVisible = false;
          this.pauseAnimation();
        }
      });
    }, options);
    
    this.observer.observe(this.widget);
  }
  
  setupEventListeners() {
    // Handle hover trigger
    if (this.animationTrigger === 'on-hover') {
      this.widget.addEventListener('mouseenter', () => {
        this.startAnimation();
      });
      
      this.widget.addEventListener('mouseleave', () => {
        this.pauseAnimation();
      });
    }
    
    // Handle window resize
    window.addEventListener('resize', this.debounce(() => {
      this.recalculateAnimation();
    }, 250));
    
    // Handle visibility change (tab switching)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.pauseAnimation();
      } else if (this.isVisible) {
        this.startAnimation();
      }
    });
  }
  
  duplicateItems() {
    // Duplicate items to create seamless loop
    const itemsClone = Array.from(this.items).map(item => item.cloneNode(true));
    itemsClone.forEach(clone => {
      this.track.appendChild(clone);
    });
  }
  
  startAnimation() {
    if (this.isAnimating) return;
    
    this.track.classList.remove('paused');
    this.isAnimating = true;
    
    // Trigger custom event
    this.widget.dispatchEvent(new CustomEvent('textScrolling:start', {
      detail: { widget: this }
    }));
  }
  
  pauseAnimation() {
    if (!this.isAnimating) return;
    
    this.track.classList.add('paused');
    this.isAnimating = false;
    
    // Trigger custom event
    this.widget.dispatchEvent(new CustomEvent('textScrolling:pause', {
      detail: { widget: this }
    }));
  }
  
  recalculateAnimation() {
    // Recalculate animation duration based on content width
    const trackWidth = this.track.scrollWidth;
    const containerWidth = this.container.offsetWidth;
    const totalDistance = trackWidth + containerWidth;
    
    // Adjust speed based on content length
    const baseSpeed = parseFloat(this.scrollSpeed);
    const adjustedSpeed = (totalDistance / 1000) * baseSpeed;
    
    this.track.style.animationDuration = `${adjustedSpeed}s`;
  }
  
  // Utility function for debouncing
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
  
  // Public methods for external control
  play() {
    this.startAnimation();
  }
  
  pause() {
    this.pauseAnimation();
  }
  
  setSpeed(speed) {
    this.scrollSpeed = speed;
    this.track.style.animationDuration = `${speed}s`;
  }
  
  setDirection(direction) {
    this.scrollDirection = direction;
    this.track.classList.remove('animate-left-to-right', 'animate-right-to-left');
    
    const animationClass = direction === 'left-to-right' 
      ? 'animate-left-to-right' 
      : 'animate-right-to-left';
    
    this.track.classList.add(animationClass);
  }
  
  destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
    
    // Remove event listeners
    window.removeEventListener('resize', this.recalculateAnimation);
    
    // Clean up
    this.widget.classList.remove('loading', 'loaded');
    this.track.classList.remove('animate-left-to-right', 'animate-right-to-left', 'paused');
  }
}

// Initialize all text scrolling widgets when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  const widgets = document.querySelectorAll('.text-scrolling-widget');
  const widgetInstances = [];
  
  widgets.forEach(widget => {
    const instance = new TextScrollingWidget(widget);
    widgetInstances.push(instance);
    
    // Store instance on element for external access
    widget.textScrollingInstance = instance;
  });
  
  // Global access for debugging/external control
  window.textScrollingWidgets = widgetInstances;
});

// Handle Shopify section reloads (theme editor)
document.addEventListener('shopify:section:load', function(event) {
  const widgets = event.target.querySelectorAll('.text-scrolling-widget');
  
  widgets.forEach(widget => {
    if (!widget.textScrollingInstance) {
      const instance = new TextScrollingWidget(widget);
      widget.textScrollingInstance = instance;
      
      if (window.textScrollingWidgets) {
        window.textScrollingWidgets.push(instance);
      }
    }
  });
});

document.addEventListener('shopify:section:unload', function(event) {
  const widgets = event.target.querySelectorAll('.text-scrolling-widget');
  
  widgets.forEach(widget => {
    if (widget.textScrollingInstance) {
      widget.textScrollingInstance.destroy();
      widget.textScrollingInstance = null;
    }
  });
});