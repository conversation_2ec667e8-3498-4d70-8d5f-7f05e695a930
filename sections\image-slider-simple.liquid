{% comment %}
  Simple Image Slider Widget - Guaranteed to work in Shopify
{% endcomment %}

<div class="simple-image-slider" id="slider-{{ section.id }}">
  <div class="slider-container">
    {% if section.settings.show_navigation and section.blocks.size > 1 %}
      <button class="slider-nav prev-btn" onclick="changeSlide(-1)">‹</button>
    {% endif %}
    
    <div class="slider-wrapper">
      <div class="slider-track" id="track-{{ section.id }}">
        {% for block in section.blocks %}
          <div class="slide" {{ block.shopify_attributes }}>
            {% if block.settings.image %}
              <div class="slide-image-container">
                {% if block.settings.link != blank %}
                  <a href="{{ block.settings.link }}">
                {% endif %}
                
                <img src="{{ block.settings.image | image_url: width: 800, height: 600 }}"
                     alt="{{ block.settings.alt_text | default: block.settings.title | default: 'Slide image' }}"
                     width="800"
                     height="600"
                     loading="lazy">
                
                {% if block.settings.link != blank %}
                  </a>
                {% endif %}
                
                {% if block.settings.title != blank or block.settings.description != blank %}
                  <div class="slide-content">
                    {% if block.settings.title != blank %}
                      <h3>{{ block.settings.title }}</h3>
                    {% endif %}
                    {% if block.settings.description != blank %}
                      <p>{{ block.settings.description }}</p>
                    {% endif %}
                  </div>
                {% endif %}
              </div>
            {% endif %}
          </div>
        {% endfor %}
      </div>
    </div>
    
    {% if section.settings.show_navigation and section.blocks.size > 1 %}
      <button class="slider-nav next-btn" onclick="changeSlide(1)">›</button>
    {% endif %}
  </div>
  
  {% if section.settings.show_dots and section.blocks.size > 1 %}
    <div class="slider-dots">
      {% for block in section.blocks %}
        <button class="dot" onclick="currentSlide({{ forloop.index }})"></button>
      {% endfor %}
    </div>
  {% endif %}
</div>

<style>
  .simple-image-slider {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: {{ section.settings.padding_top }}px 20px {{ section.settings.padding_bottom }}px;
    position: relative;
  }
  
  .slider-container {
    position: relative;
    display: flex;
    align-items: center;
  }
  
  .slider-wrapper {
    width: 100%;
    overflow: hidden;
    border-radius: {{ section.settings.border_radius }}px;
  }
  
  .slider-track {
    display: flex;
    transition: transform 0.5s ease;
    height: {{ section.settings.height }}px;
  }
  
  .slide {
    min-width: 100%;
    height: 100%;
    position: relative;
  }
  
  .slide-image-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
  }
  
  .slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .slide-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.7));
    color: white;
    padding: 30px 20px 20px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
  }
  
  .slide:hover .slide-content {
    transform: translateY(0);
  }
  
  .slide-content h3 {
    margin: 0 0 10px 0;
    font-size: 1.5rem;
  }
  
  .slide-content p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
  }
  
  .slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255,255,255,0.9);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 24px;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
  }
  
  .slider-nav:hover {
    background: white;
    transform: translateY(-50%) scale(1.1);
  }
  
  .prev-btn {
    left: 10px;
  }
  
  .next-btn {
    right: 10px;
  }
  
  .slider-dots {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
  }
  
  .dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: rgba(0,0,0,0.3);
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .dot.active {
    background: #333;
    transform: scale(1.2);
  }
  
  @media (max-width: 768px) {
    .slider-track {
      height: {{ section.settings.mobile_height }}px;
    }
    
    .slider-nav {
      width: 40px;
      height: 40px;
      font-size: 18px;
    }
    
    .slide-content {
      padding: 20px 15px 15px;
    }
    
    .slide-content h3 {
      font-size: 1.2rem;
    }
  }
</style>

<script>
  let currentSlideIndex = 0;
  const slides = document.querySelectorAll('#slider-{{ section.id }} .slide');
  const dots = document.querySelectorAll('#slider-{{ section.id }} .dot');
  const track = document.getElementById('track-{{ section.id }}');
  
  function showSlide(index) {
    if (!track || slides.length === 0) return;
    
    currentSlideIndex = index;
    if (currentSlideIndex >= slides.length) currentSlideIndex = 0;
    if (currentSlideIndex < 0) currentSlideIndex = slides.length - 1;
    
    track.style.transform = `translateX(-${currentSlideIndex * 100}%)`;
    
    dots.forEach((dot, i) => {
      dot.classList.toggle('active', i === currentSlideIndex);
    });
  }
  
  function changeSlide(direction) {
    showSlide(currentSlideIndex + direction);
  }
  
  function currentSlide(index) {
    showSlide(index - 1);
  }
  
  // Initialize
  document.addEventListener('DOMContentLoaded', function() {
    showSlide(0);
    
    {% if section.settings.autoplay %}
      setInterval(() => {
        changeSlide(1);
      }, {{ section.settings.autoplay_speed }});
    {% endif %}
  });
</script>

{% schema %}
{
  "name": "Simple Image Slider",
  "tag": "section",
  "class": "simple-slider-section",
  "settings": [
    {
      "type": "header",
      "content": "Slider Settings"
    },
    {
      "type": "range",
      "id": "height",
      "min": 200,
      "max": 800,
      "step": 50,
      "unit": "px",
      "label": "Slider Height",
      "default": 400
    },
    {
      "type": "range",
      "id": "mobile_height",
      "min": 150,
      "max": 500,
      "step": 25,
      "unit": "px",
      "label": "Mobile Height",
      "default": 300
    },
    {
      "type": "range",
      "id": "border_radius",
      "min": 0,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Border Radius",
      "default": 10
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 10,
      "unit": "px",
      "label": "Top Padding",
      "default": 50
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 10,
      "unit": "px",
      "label": "Bottom Padding",
      "default": 50
    },
    {
      "type": "checkbox",
      "id": "show_navigation",
      "label": "Show Navigation Arrows",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_dots",
      "label": "Show Dots",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Auto Play",
      "default": false
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "min": 2000,
      "max": 10000,
      "step": 1000,
      "unit": "ms",
      "label": "Auto Play Speed",
      "default": 5000
    }
  ],
  "blocks": [
    {
      "type": "slide",
      "name": "Slide",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "text",
          "id": "alt_text",
          "label": "Alt Text"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Description"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Simple Image Slider",
      "blocks": [
        {
          "type": "slide"
        },
        {
          "type": "slide"
        },
        {
          "type": "slide"
        }
      ]
    }
  ]
}
{% endschema %}
