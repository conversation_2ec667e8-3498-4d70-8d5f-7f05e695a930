# Image Slider Widget - Quick Installation Guide

## Step 1: Upload the Section File

1. **Access Theme Editor**
   - Go to Shopify Admin → Online Store → Themes
   - Click "Actions" → "Edit code" on your active theme

2. **Add New Section**
   - In the "Sections" folder, click "Add a new section"
   - Name it: `image-slider-widget.liquid`
   - Copy the entire content from `sections/image-slider-widget.liquid`
   - Click "Save"

## Step 2: Add to Your Page

### Option A: Using Theme Editor (Recommended)
1. Go to Online Store → Themes → Customize
2. Navigate to the page where you want the slider (e.g., Contact Us page)
3. Click "Add section"
4. Select "Image Slider Widget"
5. Configure your settings and add slides

### Option B: Direct Template Edit
1. In the code editor, find your template file (e.g., `page.contact.liquid`)
2. Add this line where you want the slider:
   ```liquid
   {% section 'image-slider-widget' %}
   ```
3. Save the file

## Step 3: Configure the Slider

1. **Basic Settings**
   - Set slider height for desktop and mobile
   - Choose slides per view (1-4 for desktop, 1-2 for mobile)
   - Enable/disable auto-play and navigation

2. **Add Your Images**
   - Click "Add block" → "Slide"
   - Upload your image (recommended: 1200x900px)
   - Add title, description, and alt text
   - Optionally add a link URL
   - Repeat for additional slides

3. **Customize Appearance**
   - Adjust border radius for rounded corners
   - Set colors for navigation and pagination
   - Configure content overlay opacity

## Step 4: Test and Optimize

1. **Preview Your Changes**
   - Use the theme editor preview
   - Test on different device sizes
   - Check auto-play and navigation functionality

2. **Performance Check**
   - Ensure images load quickly
   - Test touch/swipe on mobile devices
   - Verify accessibility features work

## Quick Configuration Tips

### For Contact Us Pages
- Use 3-5 slides showcasing your business
- Set auto-play to 5-7 seconds
- Include contact information in slide descriptions
- Link slides to relevant pages (about, services, etc.)

### Image Recommendations
- **Size**: 1200x900px (4:3 aspect ratio)
- **Format**: WebP or JPG
- **File Size**: Under 500KB each
- **Content**: High-quality, relevant images

### Mobile Optimization
- Set mobile height to 250-350px
- Use 1 slide per view on mobile
- Test touch/swipe functionality
- Ensure text is readable on small screens

## Common Settings Examples

### Showcase Slider (Hero Section)
```
- Height: 600px desktop, 400px mobile
- Slides per view: 1
- Auto-play: 6 seconds
- Navigation: Enabled
- Border radius: 20px
```

### Product Gallery
```
- Height: 400px desktop, 300px mobile
- Slides per view: 3 desktop, 1 mobile
- Auto-play: 4 seconds
- Gap between slides: 30px
- Border radius: 12px
```

### Testimonial Slider
```
- Height: 300px desktop, 250px mobile
- Slides per view: 2 desktop, 1 mobile
- Auto-play: 8 seconds
- Content overlay: 80% opacity
- Minimal border radius: 8px
```

## Troubleshooting Quick Fixes

### Slider Not Appearing
- Check that the section is added to the correct template
- Verify the section file was saved properly
- Ensure at least one slide block is added

### Images Not Loading
- Check image file sizes (max 20MB)
- Verify images are uploaded correctly
- Try re-uploading images

### Mobile Issues
- Test on actual mobile devices
- Check mobile-specific settings
- Verify touch events are working

### Performance Problems
- Reduce image file sizes
- Limit to 5-8 slides maximum
- Check for theme conflicts

## Need Help?

- Review the full README.md for detailed documentation
- Check browser console for error messages
- Test in different browsers and devices
- Refer to Shopify's theme development documentation

## Success Checklist

- [ ] Section file uploaded and saved
- [ ] Slider added to desired page/template
- [ ] At least 3 slides configured with images
- [ ] Alt text added for accessibility
- [ ] Mobile responsiveness tested
- [ ] Auto-play timing adjusted
- [ ] Colors match your brand
- [ ] Performance tested on slow connections

Your image slider widget is now ready to enhance your Shopify store's visual appeal and user experience!
