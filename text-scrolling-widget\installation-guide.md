# Text Scrolling Widget - Installation Guide

## Step 1: Upload Files

1. **Upload Section File**
   - Go to Shopify Admin → Online Store → Themes → Actions → Edit code
   - In the "Sections" folder, click "Add a new section"
   - Name it `text-scrolling.liquid`
   - Copy and paste the content from `sections/text-scrolling.liquid`

2. **Upload CSS File**
   - In the "Assets" folder, click "Add a new asset"
   - Upload or create `text-scrolling.css`
   - Copy and paste the content from `assets/text-scrolling.css`

3. **Upload JavaScript File**
   - In the "Assets" folder, click "Add a new asset"
   - Upload or create `text-scrolling.js`
   - Copy and paste the content from `assets/text-scrolling.js`

## Step 2: Add to Theme.liquid

1. **Add CSS Reference**
   - Open `layout/theme.liquid`
   - In the `<head>` section, after existing stylesheets, add:
   ```liquid
   {{ 'text-scrolling.css' | asset_url | stylesheet_tag }}
   ```

2. **Add JavaScript Reference**
   - Before the closing `</body>` tag, add:
   ```liquid
   <script src="{{ 'text-scrolling.js' | asset_url }}" defer></script>
   ```

## Step 3: Add Section to Template

### Option 1: Using Theme Editor
1. Go to Online Store → Themes → Customize
2. Navigate to the page where you want the scrolling text
3. Click "Add section"
4. Select "Text Scrolling Widget"
5. Configure your settings

### Option 2: Direct Template Edit
Add this code to your template file:
```liquid
{% section 'text-scrolling' %}
```

## Step 4: Configure the Widget

1. **Add Text Items**
   - Click "Add text item" to add scrolling text
   - Enter your text content
   - Customize colors and styling for each item

2. **Configure Animation**
   - Choose scroll direction (left-to-right or right-to-left)
   - Set scroll speed (5-50 seconds)
   - Select animation trigger (on scroll, on load, or on hover)

3. **Style the Section**
   - Set section height and padding
   - Choose background color or gradient
   - Adjust text size and weight

## Features Overview

- **Multiple Text Items**: Add unlimited scrolling text items
- **Individual Styling**: Each text item can have unique colors and effects
- **Gradient Text**: Apply custom gradients to text
- **Text Effects**: Add shadows and strokes
- **Responsive Design**: Automatically adapts to mobile devices
- **Performance Optimized**: Uses CSS animations and intersection observer
- **Accessibility**: Respects reduced motion preferences

## Customization Options

- Section height and padding
- Scroll direction and speed
- Animation triggers
- Text size and weight
- Background colors and gradients
- Individual text colors and gradients
- Text shadows and strokes