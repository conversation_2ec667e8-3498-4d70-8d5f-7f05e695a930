# Custom Footer Widget - Responsive Design Guide

This guide explains the responsive design implementation of the custom footer widget for Shopify Atelier Theme v2.1.6, helping you understand how it adapts across different device sizes.

## Responsive Breakpoints

The footer uses these standard breakpoints to ensure optimal display across devices:

| Breakpoint | Device Type | Column Layout | Features |
|------------|-------------|---------------|----------|
| >1200px | Large Desktop | 5 columns | Full features |
| 991-1200px | Desktop | 4 columns | Full features |
| 767-991px | Tablet Landscape | 3 columns | Full features |
| 480-767px | Tablet Portrait / Mobile Landscape | 2 columns | Accordion menus |
| <480px | Mobile Portrait | 1 column | Accordion menus |

## Desktop Experience (>991px)

On desktop devices, the footer displays in its full form:

- **Layout**: Multi-column grid (4-5 columns depending on screen width)
- **Navigation**: All menu items visible
- **Content**: All sections fully expanded
- **Features**: Newsletter form, social icons, and payment methods all visible

## Tablet Experience (767-991px)

On tablet devices in landscape orientation:

- **Layout**: 3-column grid
- **Navigation**: All menu items visible
- **Content**: Slightly condensed spacing
- **Features**: All features visible, possibly with adjusted sizing

## Mobile Experience (<767px)

On smaller tablets and mobile devices:

- **Layout**: 1-2 column grid
- **Navigation**: Accordion-style menus (click to expand/collapse)
- **Content**: Vertically stacked, prioritized content
- **Features**: Simplified layout with focus on essential elements
- **Bottom Bar**: Stacked copyright and payment icons

### Mobile Accordion Functionality

The mobile experience includes accordion-style menus:

1. **Default State**: Menu items are hidden, only headings visible
2. **Interaction**: Tap heading or toggle icon to expand/collapse menu
3. **Visual Cues**: Plus/minus icons indicate expandable sections
4. **Animation**: Smooth transitions for expand/collapse actions

## CSS Implementation

The responsive behavior is controlled through CSS media queries:

```css
/* Desktop - 5 columns */
.footer-custom-column {
  width: 20%;
}

/* Desktop - 4 columns */
@media (max-width: 1200px) {
  .footer-custom-column {
    width: 25%;
  }
}

/* Tablet Landscape - 3 columns */
@media (max-width: 991px) {
  .footer-custom-column {
    width: 33.333%;
  }
}

/* Tablet Portrait / Mobile - 2 columns */
@media (max-width: 767px) {
  .footer-custom-column {
    width: 50%;
  }
  
  /* Mobile accordion functionality */
  .footer-custom-mobile-toggle {
    display: block;
  }
  
  .footer-custom-menu {
    display: none;
  }
  
  .footer-custom-menu.active {
    display: block;
  }
  
  /* Stacked bottom bar */
  .footer-custom-bottom {
    flex-direction: column;
  }
}

/* Mobile Portrait - 1 column */
@media (max-width: 480px) {
  .footer-custom-column {
    width: 100%;
  }
}
```

## JavaScript Functionality

The mobile accordion functionality is implemented with this JavaScript:

```javascript
document.addEventListener('DOMContentLoaded', function() {
  // Select all mobile toggle buttons
  const toggles = document.querySelectorAll('.footer-custom-mobile-toggle');
  
  // Add click event listeners
  toggles.forEach(toggle => {
    toggle.addEventListener('click', function() {
      // Find the related menu
      const menu = this.closest('.footer-custom-column').querySelector('.footer-custom-menu');
      
      // Toggle active classes
      this.classList.toggle('active');
      menu.classList.toggle('active');
    });
  });
});
```

## Responsive Images

The background image adapts responsively:

1. **Implementation**: CSS `background-size: cover` ensures proper scaling
2. **Loading**: Shopify's image URL filters optimize for different screens
3. **Visibility**: Overlay opacity may be adjusted on smaller screens

## Typography Scaling

Text sizes adjust across breakpoints:

1. **Headings**: Slightly smaller on mobile devices
2. **Body Text**: Optimized for readability on each device size
3. **Spacing**: Increased line height on smaller screens for better readability

## Testing Your Footer

To ensure your footer looks great on all devices:

### Desktop Testing

1. **Large Desktop** (>1200px)
   - Verify 5-column layout
   - Check that all content fits properly
   - Ensure spacing is balanced

2. **Standard Desktop** (991-1200px)
   - Verify 4-column layout
   - Check for any content overflow
   - Ensure all features remain accessible

### Tablet Testing

1. **Landscape Orientation** (767-991px)
   - Verify 3-column layout
   - Check that all content remains readable
   - Ensure touch targets are large enough

2. **Portrait Orientation** (480-767px)
   - Verify 2-column layout
   - Test accordion functionality
   - Check for proper spacing between elements

### Mobile Testing

1. **Standard Mobile** (<480px)
   - Verify single column layout
   - Test accordion expand/collapse
   - Check that all text is readable without zooming
   - Verify touch targets are at least 44px in size

2. **Small Mobile** (<375px)
   - Check for any horizontal scrolling (should be none)
   - Verify all content fits within screen
   - Ensure text remains readable

## Common Responsive Issues

### Horizontal Scrolling

If mobile users experience unwanted horizontal scrolling:

- Check for elements with fixed widths
- Ensure all containers use max-width with percentage values
- Verify no negative margins causing overflow

### Touch Target Size

For optimal mobile usability:

- All clickable elements should be at least 44x44px
- Add additional padding to links if needed
- Ensure adequate spacing between touch targets

### Content Hierarchy

On mobile devices:

- Most important content should appear first
- Consider hiding less critical elements on smallest screens
- Use the accordion pattern for secondary content

## Custom Responsive Adjustments

If you need to customize the responsive behavior:

### Adjusting Breakpoints

To change when the layout shifts:

1. Modify the media query values in the CSS
2. Test thoroughly at various screen sizes
3. Update the JavaScript if accordion behavior needs adjustment

### Changing Column Layout

To modify how columns are arranged:

1. Adjust the width percentages in the CSS
2. Update the media query rules to match your preferences
3. Consider content hierarchy when changing layouts

### Mobile-Specific Styling

To add custom mobile styles:

1. Add new rules within the appropriate media queries
2. Use mobile-first approach for new features
3. Test on actual devices, not just browser emulation

## Best Practices

1. **Test on Real Devices**
   - Use actual phones and tablets, not just emulators
   - Test on both iOS and Android devices
   - Verify on multiple browsers

2. **Consider Connection Speed**
   - Optimize images for mobile connections
   - Minimize CSS and JavaScript
   - Use lazy loading where appropriate

3. **Touch-Friendly Design**
   - Ensure adequate spacing between clickable elements
   - Make touch targets large enough (44x44px minimum)
   - Provide visual feedback for touch interactions

4. **Accessibility**
   - Maintain sufficient color contrast at all sizes
   - Ensure text remains readable without zooming
   - Verify keyboard navigation works on all devices

5. **Performance**
   - Monitor load times on mobile connections
   - Optimize resources for mobile devices
   - Consider conditional loading for mobile-specific features
