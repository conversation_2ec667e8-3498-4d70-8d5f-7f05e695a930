# Text Scrolling Widget for Atelier Theme v2.1.6

This widget creates animated scrolling text that triggers when users scroll to the section. Features include:

- Left-to-right or right-to-left scrolling
- Custom colors and gradient backgrounds
- Multiple text items with individual styling
- Responsive design
- Scroll-triggered animations

## Installation

1. Upload `sections/text-scrolling.liquid` to your sections folder
2. Upload `assets/text-scrolling.css` to your assets folder  
3. Upload `assets/text-scrolling.js` to your assets folder
4. Add CSS and JS references to your theme.liquid file
5. Add the section to your desired template

## Usage

Add the section through the theme editor or by adding `{% section 'text-scrolling' %}` to your template.