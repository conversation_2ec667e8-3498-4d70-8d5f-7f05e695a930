{% comment %}
  Text Scrolling Widget for Atelier Theme v2.1.6
  Animated scrolling text with customizable colors and gradients
{% endcomment %}

<div class="text-scrolling-widget" 
     id="text-scrolling-{{ section.id }}"
     data-scroll-direction="{{ section.settings.scroll_direction }}"
     data-scroll-speed="{{ section.settings.scroll_speed }}"
     data-animation-trigger="{{ section.settings.animation_trigger }}">
  
  <div class="text-scrolling-container">
    <div class="text-scrolling-track">
      {% for block in section.blocks %}
        <div class="text-scrolling-item" 
             data-block-id="{{ block.id }}"
             {{ block.shopify_attributes }}>
          <span class="text-scrolling-content">{{ block.settings.text_content }}</span>
        </div>
      {% endfor %}
    </div>
  </div>
</div>

<style>
  #text-scrolling-{{ section.id }} {
    --section-bg-color: {{ section.settings.section_bg_color }};
    --section-bg-gradient: {{ section.settings.section_bg_gradient }};
    --section-padding-top: {{ section.settings.section_padding_top }}px;
    --section-padding-bottom: {{ section.settings.section_padding_bottom }}px;
    --section-height: {{ section.settings.section_height }}px;
    --text-size: {{ section.settings.text_size }}px;
    --text-weight: {{ section.settings.text_weight }};
    --scroll-speed: {{ section.settings.scroll_speed }}s;
  }
  
  {% for block in section.blocks %}
    #text-scrolling-{{ section.id }} [data-block-id="{{ block.id }}"] .text-scrolling-content {
      color: {{ block.settings.text_color }};
      {% if block.settings.text_gradient != blank %}
        background: {{ block.settings.text_gradient }};
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      {% endif %}
      {% if block.settings.text_shadow != blank %}
        text-shadow: {{ block.settings.text_shadow }};
      {% endif %}
      {% if block.settings.text_stroke_width > 0 %}
        -webkit-text-stroke: {{ block.settings.text_stroke_width }}px {{ block.settings.text_stroke_color }};
      {% endif %}
    }
  {% endfor %}
</style>

{% schema %}
{
  "name": "Text Scrolling Widget",
  "tag": "section",
  "class": "text-scrolling-section",
  "settings": [
    {
      "type": "header",
      "content": "Layout Settings"
    },
    {
      "type": "range",
      "id": "section_height",
      "min": 100,
      "max": 500,
      "step": 10,
      "unit": "px",
      "label": "Section Height",
      "default": 200
    },
    {
      "type": "range",
      "id": "section_padding_top",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Top Padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "section_padding_bottom",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Bottom Padding",
      "default": 40
    },
    {
      "type": "header",
      "content": "Animation Settings"
    },
    {
      "type": "select",
      "id": "scroll_direction",
      "label": "Scroll Direction",
      "options": [
        {
          "value": "left-to-right",
          "label": "Left to Right"
        },
        {
          "value": "right-to-left",
          "label": "Right to Left"
        }
      ],
      "default": "left-to-right"
    },
    {
      "type": "range",
      "id": "scroll_speed",
      "min": 5,
      "max": 50,
      "step": 1,
      "unit": "s",
      "label": "Scroll Speed",
      "default": 20
    },
    {
      "type": "select",
      "id": "animation_trigger",
      "label": "Animation Trigger",
      "options": [
        {
          "value": "on-scroll",
          "label": "When Section Comes Into View"
        },
        {
          "value": "on-load",
          "label": "When Page Loads"
        },
        {
          "value": "on-hover",
          "label": "On Hover"
        }
      ],
      "default": "on-scroll"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 16,
      "max": 120,
      "step": 2,
      "unit": "px",
      "label": "Text Size",
      "default": 48
    },
    {
      "type": "select",
      "id": "text_weight",
      "label": "Text Weight",
      "options": [
        {
          "value": "300",
          "label": "Light"
        },
        {
          "value": "400",
          "label": "Normal"
        },
        {
          "value": "500",
          "label": "Medium"
        },
        {
          "value": "600",
          "label": "Semi Bold"
        },
        {
          "value": "700",
          "label": "Bold"
        },
        {
          "value": "800",
          "label": "Extra Bold"
        }
      ],
      "default": "600"
    },
    {
      "type": "header",
      "content": "Background"
    },
    {
      "type": "color",
      "id": "section_bg_color",
      "label": "Background Color",
      "default": "#ffffff"
    },
    {
      "type": "color_background",
      "id": "section_bg_gradient",
      "label": "Background Gradient"
    }
  ],
  "blocks": [
    {
      "type": "text_item",
      "name": "Text Item",
      "settings": [
        {
          "type": "text",
          "id": "text_content",
          "label": "Text Content",
          "default": "Scrolling Text"
        },
        {
          "type": "header",
          "content": "Text Styling"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text Color",
          "default": "#000000"
        },
        {
          "type": "color_background",
          "id": "text_gradient",
          "label": "Text Gradient (overrides color)"
        },
        {
          "type": "text",
          "id": "text_shadow",
          "label": "Text Shadow (CSS format)",
          "info": "Example: 2px 2px 4px rgba(0,0,0,0.3)",
          "placeholder": "2px 2px 4px rgba(0,0,0,0.3)"
        },
        {
          "type": "header",
          "content": "Text Stroke"
        },
        {
          "type": "range",
          "id": "text_stroke_width",
          "min": 0,
          "max": 5,
          "step": 0.5,
          "unit": "px",
          "label": "Stroke Width",
          "default": 0
        },
        {
          "type": "color",
          "id": "text_stroke_color",
          "label": "Stroke Color",
          "default": "#000000"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Text Scrolling Widget",
      "blocks": [
        {
          "type": "text_item",
          "settings": {
            "text_content": "Welcome to Our Store"
          }
        },
        {
          "type": "text_item",
          "settings": {
            "text_content": "Premium Quality Products"
          }
        },
        {
          "type": "text_item",
          "settings": {
            "text_content": "Fast Worldwide Shipping"
          }
        }
      ]
    }
  ]
}
{% endschema %}