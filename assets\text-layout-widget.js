/**
 * Text Layout Widget JavaScript
 * Compatible with Atelier Theme v2.1.6
 * Handles responsive typography and animations
 */

class TextLayoutWidget {
  constructor(element) {
    this.widget = element;
    this.blocks = element.querySelectorAll('.text-layout-block');
    this.container = element.querySelector('.text-layout-container');
    
    this.init();
  }
  
  init() {
    this.setupResponsiveText();
    this.setupIntersectionObserver();
    this.setupEventListeners();
  }
  
  setupResponsiveText() {
    this.blocks.forEach(block => {
      const content = block.querySelector('.text-layout-content');
      const fontSize = window.getComputedStyle(content).fontSize;
      content.style.setProperty('--responsive-font-size', fontSize);
    });
  }
  
  setupIntersectionObserver() {
    const options = {
      root: null,
      rootMargin: '0px 0px -100px 0px',
      threshold: 0.1
    };
    
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('in-view');
          
          // Trigger custom event
          this.widget.dispatchEvent(new CustomEvent('textLayout:blockVisible', {
            detail: { block: entry.target, widget: this }
          }));
        }
      });
    }, options);
    
    this.blocks.forEach(block => {
      this.observer.observe(block);
    });
  }
  
  setupEventListeners() {
    // Handle window resize
    window.addEventListener('resize', this.debounce(() => {
      this.setupResponsiveText();
    }, 250));
    
    // Handle orientation change
    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        this.setupResponsiveText();
      }, 100);
    });
  }
  
  // Utility function for debouncing
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
  
  // Public methods for external control
  updateText(blockIndex, newText) {
    if (this.blocks[blockIndex]) {
      const content = this.blocks[blockIndex].querySelector('.text-layout-content');
      content.textContent = newText;
    }
  }
  
  updateAlignment(blockIndex, alignment) {
    if (this.blocks[blockIndex]) {
      this.blocks[blockIndex].setAttribute('data-alignment', alignment);
    }
  }
  
  updateColor(blockIndex, color) {
    if (this.blocks[blockIndex]) {
      const content = this.blocks[blockIndex].querySelector('.text-layout-content');
      content.style.color = color;
    }
  }
  
  updateFontSize(blockIndex, size) {
    if (this.blocks[blockIndex]) {
      const content = this.blocks[blockIndex].querySelector('.text-layout-content');
      content.style.fontSize = size + 'px';
      content.style.setProperty('--responsive-font-size', size + 'px');
    }
  }
  
  destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
    
    // Remove event listeners
    window.removeEventListener('resize', this.setupResponsiveText);
    window.removeEventListener('orientationchange', this.setupResponsiveText);
  }
}

// Initialize all text layout widgets when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  const widgets = document.querySelectorAll('.text-layout-widget');
  const widgetInstances = [];
  
  widgets.forEach(widget => {
    const instance = new TextLayoutWidget(widget);
    widgetInstances.push(instance);
    
    // Store instance on element for external access
    widget.textLayoutInstance = instance;
  });
  
  // Global access for debugging/external control
  window.textLayoutWidgets = widgetInstances;
});

// Handle Shopify section reloads (theme editor)
document.addEventListener('shopify:section:load', function(event) {
  const widgets = event.target.querySelectorAll('.text-layout-widget');
  
  widgets.forEach(widget => {
    if (!widget.textLayoutInstance) {
      const instance = new TextLayoutWidget(widget);
      widget.textLayoutInstance = instance;
      
      if (window.textLayoutWidgets) {
        window.textLayoutWidgets.push(instance);
      }
    }
  });
});

document.addEventListener('shopify:section:unload', function(event) {
  const widgets = event.target.querySelectorAll('.text-layout-widget');
  
  widgets.forEach(widget => {
    if (widget.textLayoutInstance) {
      widget.textLayoutInstance.destroy();
      widget.textLayoutInstance = null;
    }
  });
});