# Custom Footer Implementation Checklist

Follow these steps to successfully implement your custom footer in your Shopify Atelier theme:

## Step 1: Upload Files
- [ ] Upload `footer-custom.liquid` (or `footer-custom-all-in-one.liquid`) to the `sections` folder
- [ ] Upload `custom-footer.css` to the `assets` folder
- [ ] Upload `custom-footer.js` to the `assets` folder
- [ ] Upload all icon snippet files to the `snippets` folder:
  - [ ] `icon-facebook.liquid`
  - [ ] `icon-instagram.liquid`
  - [ ] `icon-pinterest.liquid`
  - [ ] `icon-twitter.liquid`
  - [ ] `icon-youtube.liquid`
  - [ ] `icon-linkedin.liquid`

## Step 2: Modify Theme.liquid
- [ ] Open your `layout/theme.liquid` file
- [ ] Move `{{ 'custom-footer.css' | asset_url | stylesheet_tag }}` to be right after `{%- render 'stylesheets' -%}`
- [ ] Keep `{{ 'custom-footer.js' | asset_url | script_tag }}` just before the closing `</body>` tag

## Step 3: Set Up Navigation Menus
- [ ] Go to Shopify Admin > Online Store > Navigation
- [ ] Create the following link lists:
  - [ ] Our Collections (for column 1)
  - [ ] Quick Links (for column 2)
  - [ ] Customer Service (for column 3)
  - [ ] Information (for column 4)
  - [ ] Footer Bottom (for bottom links)

## Step 4: Add Footer to Theme
Choose one of these options:

### Option A: Using the Theme Editor
- [ ] Go to Shopify Admin > Online Store > Themes
- [ ] Click "Customize" on your active theme
- [ ] In the footer section, add the "Custom Footer" section

### Option B: Using Code Editor
- [ ] Go to Online Store > Themes > Actions > Edit code
- [ ] Find the file that controls your footer (typically in your `sections/footer-group` folder)
- [ ] Add `{% section 'footer-custom' %}` or `{% section 'footer-custom-all-in-one' %}` at the appropriate location

## Step 5: Configure Your Footer
- [ ] Set up column titles and menus
- [ ] Add contact information (address, phone, email)
- [ ] Configure social media links in Theme Settings
- [ ] Set copyright text and designer credit
- [ ] Select background color and/or image

## Step 6: Testing
- [ ] Test on desktop to ensure all content displays properly
- [ ] Test on tablet to verify responsiveness
- [ ] Test on mobile to confirm accordion functionality works
- [ ] Verify all links are working correctly

## Additional Resources
- Refer to the README.md file for detailed instructions and troubleshooting tips
- See theme-liquid-instructions.md for specific guidance on fixing the theme.liquid file
- Use the corrected-theme.liquid file as a reference for proper implementation

## Notes
If you're using the all-in-one version (`footer-custom-all-in-one.liquid`), you don't need to upload the separate CSS and JS files or modify theme.liquid, as everything is contained in the single section file.
