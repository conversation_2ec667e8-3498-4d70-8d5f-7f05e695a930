# Custom Footer Widget Setup Guide for Atelier Theme v2.1.6

This guide will help you implement the custom footer widget in your Shopify store using the Atelier theme. Follow these steps carefully to ensure proper installation.

## Files Overview

1. **Section File**: `sections/footer-custom.liquid` - The main section template for the footer
2. **CSS File**: `assets/custom-footer.css` - Styling for the footer
3. **JavaScript File**: `assets/custom-footer.js` - Mobile functionality and dynamic styling
4. **Icon Snippets**: Various icon files in the `snippets` folder for social media icons

## Step 1: Upload Files to Your Theme

1. Go to your Shopify admin dashboard
2. Navigate to **Online Store** > **Themes**
3. Find your active Atelier theme and click **Actions** > **Edit code**
4. Upload each file to its respective folder:
   - Upload `footer-custom.liquid` to the `sections` folder
   - Upload `custom-footer.css` and `custom-footer.js` to the `assets` folder
   - Upload all icon snippet files to the `snippets` folder

## Step 2: Include CSS and JavaScript in Theme.liquid

1. Open your `layout/theme.liquid` file
2. Add the following code in the `<head>` section after the existing stylesheets (after the `{%- render 'stylesheets' -%}` line):

```liquid
{{ 'custom-footer.css' | asset_url | stylesheet_tag }}
```

3. Add the following code just before the closing `</body>` tag and after other scripts:

```liquid
<script src="{{ 'custom-footer.js' | asset_url }}" defer></script>
```

## Step 3: Create Link Lists for Footer Menus

1. Go to **Online Store** > **Navigation**
2. Create the following link lists (menus) for your footer:
   - **Our Collections** - For column 1
   - **Quick Links** - For column 2
   - **Customer Service** - For column 3
   - **Information** - For column 4
   - **Footer Bottom** - For links in the footer bottom

## Step 4: Add the Section to Your Theme

### Option 1: Using the Theme Editor

1. Go to **Online Store** > **Themes**
2. Click **Customize** on your active theme
3. On the left sidebar, click **Add section**
4. Search for and select **Custom Footer**
5. Configure the section settings as desired

### Option 2: Directly Edit Theme File

1. Go to **Online Store** > **Themes** > **Actions** > **Edit code**
2. Open your `templates/page.liquid` or other template where you want to add the footer
3. Add the following code where you want the footer to appear:

```liquid
{% section 'footer-custom' %}
```

## Step 5: Configure the Footer Settings

1. In the Theme Editor, find the **Custom Footer** section
2. Configure each setting:
   - **Footer Columns**: Enable/disable the column display
   - **Column Titles**: Set titles for each column
   - **Menu Selection**: Choose which link list to display in each column
   - **Contact Information**: Add your business address, phone numbers, and email
   - **Social Links**: Configure your social media links in the Theme Settings
   - **Copyright Text**: Customize the copyright statement
   - **Background**: Choose a background color or image

## Step 6: Testing and Adjustments

1. Preview your store on different devices to test responsiveness
2. Check mobile behavior for the collapsible menu functionality
3. Verify all links are working correctly
4. Make any style adjustments as needed in the `custom-footer.css` file

## Troubleshooting

### Social Media Icons Not Showing

- Make sure you've added the icon snippet files to your theme
- Verify your social media URLs are correctly entered in Theme Settings

### Menu Items Not Appearing

- Check that you've created the appropriate link lists in Navigation
- Ensure you've selected the correct menus in the section settings

### Mobile Dropdown Not Working

- Confirm that `custom-footer.js` is properly loaded
- Check browser console for any JavaScript errors

## Customization

To further customize the footer's appearance:

1. Edit the `custom-footer.css` file for styling changes
2. Modify the `custom-footer.js` file for behavior changes
3. Update the `footer-custom.liquid` file for structural changes

## Additional Notes

- The footer is fully responsive and will adapt to different screen sizes
- On mobile devices, the menu columns will collapse into accordions for better usability
- Social media icons use the built-in theme settings for consistency

If you need further assistance, please contact your theme developer or Shopify support.
