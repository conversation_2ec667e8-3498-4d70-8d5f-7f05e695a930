# Custom Footer Widget - Troubleshooting Guide

This troubleshooting guide will help you resolve common issues with the custom footer widget for Shopify Atelier Theme v2.1.6.

## Common Issues and Solutions

### Background Image Not Displaying

**Symptoms:**
- Background image is not visible
- Background appears as solid color only

**Possible Causes:**
1. Image not uploaded correctly
2. Image URL is incorrect
3. Overlay opacity is too high
4. CSS conflicts

**Solutions:**
1. **Check Image Upload:**
   - Verify the image is uploaded in the theme editor
   - Make sure the image file is not corrupted

2. **Verify Image URL:**
   - In theme editor, re-select the image
   - Check browser console for 404 errors on image requests

3. **Adjust Overlay Opacity:**
   - Reduce overlay opacity in theme settings
   - Try setting it to 50% or lower

4. **Inspect CSS:**
   - Use browser inspector to check if the image element is present
   - Verify z-index settings (background should be z-index: 0, overlay z-index: 1)

### Mobile Accordion Not Working

**Symptoms:**
- Menu items don't collapse/expand on mobile
- Toggle icons not appearing or not working

**Possible Causes:**
1. JavaScript not loading
2. JavaScript errors
3. Selector conflicts

**Solutions:**
1. **Check JavaScript Loading:**
   - Verify custom-footer.js is included and loading
   - Check browser console for loading errors

2. **Fix JavaScript Errors:**
   - Look for errors in browser console
   - Make sure jQuery is loaded if required

3. **Inspect Toggle Selectors:**
   - Verify the toggle elements have the correct classes
   - Make sure event listeners are attaching properly

### Incorrect Layout or Styling

**Symptoms:**
- Footer appears differently than expected
- Elements misaligned or sized incorrectly

**Possible Causes:**
1. CSS conflicts with theme
2. Missing CSS variables
3. Responsive breakpoints not working

**Solutions:**
1. **Resolve CSS Conflicts:**
   - Use more specific selectors in custom CSS
   - Use !important sparingly for critical styles

2. **Check CSS Variables:**
   - Verify all required CSS variables are defined
   - Look for any liquid syntax errors in variable definitions

3. **Test Responsive Breakpoints:**
   - Test on various device sizes
   - Verify media queries are working correctly

### Newsletter Form Not Working

**Symptoms:**
- Form submission errors
- No confirmation after submission

**Possible Causes:**
1. Form action URL incorrect
2. Missing form fields
3. JavaScript validation errors

**Solutions:**
1. **Check Form Configuration:**
   - Verify the form action URL matches Shopify's requirements
   - Ensure all required fields are present

2. **Test Form Submission:**
   - Try submitting with a test email
   - Check browser console for AJAX errors

3. **Verify Form Markup:**
   - Make sure the form has the correct name attributes
   - Check that required hidden fields are present

### Social Media Icons Missing or Broken

**Symptoms:**
- Icons not displaying
- Icons appearing as broken images

**Possible Causes:**
1. Icon snippets missing
2. SVG syntax errors
3. Incorrect include statements

**Solutions:**
1. **Verify Icon Snippets:**
   - Check that all required icon snippets are present in the snippets folder
   - Make sure the file names match the include statements

2. **Check SVG Syntax:**
   - Verify SVG code is valid
   - Look for missing closing tags or attributes

3. **Fix Include Statements:**
   - Ensure that include statements use the correct syntax
   - Check for typos in snippet names

## Theme-Specific Issues

### Conflicts with Atelier Theme

**Symptoms:**
- Footer not matching theme style
- Unexpected behavior or appearance

**Possible Causes:**
1. Theme version differences
2. Theme CSS overriding custom styles
3. Theme JavaScript conflicts

**Solutions:**
1. **Verify Theme Version:**
   - Confirm you're using Atelier theme v2.1.6
   - Check for any theme updates that might affect compatibility

2. **Resolve Style Conflicts:**
   - Use browser inspector to identify conflicting styles
   - Add more specific selectors to custom footer CSS

3. **Address JavaScript Conflicts:**
   - Make sure custom footer JavaScript doesn't conflict with theme JS
   - Check if theme has its own footer functionality that needs to be disabled

### Theme Editor Not Showing All Options

**Symptoms:**
- Some customization options missing
- Settings not saving correctly

**Possible Causes:**
1. Schema errors
2. Liquid syntax issues
3. Theme editor cache problems

**Solutions:**
1. **Check Schema Syntax:**
   - Verify JSON syntax in section schema
   - Look for missing commas or brackets

2. **Validate Liquid Code:**
   - Check for liquid syntax errors
   - Ensure all tags are properly closed

3. **Clear Theme Editor Cache:**
   - Save theme
   - Refresh the page
   - Try in a different browser

## Performance Issues

### Slow Loading Times

**Symptoms:**
- Footer takes longer to load than other page elements
- Visible delay in background image appearance

**Possible Causes:**
1. Large background image
2. Unoptimized CSS/JS
3. Too many custom fonts

**Solutions:**
1. **Optimize Background Image:**
   - Compress image file size
   - Use Shopify's image optimization parameters

2. **Improve CSS/JS Performance:**
   - Minimize CSS by removing unused styles
   - Defer JavaScript loading

3. **Reduce Custom Font Usage:**
   - Limit to essential font weights
   - Use system fonts where possible

### Mobile Performance Problems

**Symptoms:**
- Sluggish scrolling on mobile
- Animation stuttering

**Possible Causes:**
1. Heavy CSS animations
2. Large images not optimized for mobile
3. Too many DOM elements

**Solutions:**
1. **Optimize Animations:**
   - Use hardware-accelerated properties
   - Simplify or remove animations on mobile

2. **Use Responsive Images:**
   - Serve smaller images to mobile devices
   - Use appropriate Shopify image size parameters

3. **Simplify DOM Structure:**
   - Reduce unnecessary div nesting
   - Consider conditional loading for mobile

## Advanced Troubleshooting

If you're still experiencing issues after trying the solutions above:

1. **Compare with Original Files:**
   - Check your customized files against the original versions
   - Look for any accidental changes or deletions

2. **Isolate the Problem:**
   - Temporarily disable other theme customizations
   - Test the footer in isolation

3. **Review Browser Console:**
   - Check for any JavaScript errors
   - Look for network request failures

4. **Validate HTML Structure:**
   - Use W3C validator to check HTML
   - Fix any structural issues

5. **Theme Compatibility Check:**
   - Test with a clean installation of Atelier theme v2.1.6
   - Identify any theme-specific issues

## Getting Additional Help

If you're still experiencing issues:

1. **Document the Problem:**
   - Take screenshots of the issue
   - Note any error messages
   - Document steps to reproduce

2. **Contact Developer:**
   - Provide detailed information about the problem
   - Share your troubleshooting steps
   - Include theme version and any customizations

3. **Shopify Support:**
   - If issue might be theme-related, contact Shopify support
   - Provide details about custom section implementation
