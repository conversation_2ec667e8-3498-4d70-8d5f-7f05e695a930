# Custom Footer Widget - Developer Reference

This technical reference provides detailed information for developers who want to further customize or extend the custom footer widget for Shopify Atelier Theme v2.1.6.

## File Structure

```
├── sections/
│   ├── footer-custom.liquid         # Main section template
│   └── footer-custom-all-in-one.liquid  # Self-contained version
├── assets/
│   ├── custom-footer.css            # Stylesheet
│   └── custom-footer.js             # JavaScript for mobile functionality
└── snippets/
    ├── icon-facebook.liquid         # Social media icons
    ├── icon-instagram.liquid
    ├── icon-twitter.liquid
    ├── icon-pinterest.liquid
    ├── icon-youtube.liquid
    └── icon-linkedin.liquid
```

## Technical Implementation

### Liquid Section Schema

The section schema defines all customization options available in the theme editor:

```json
{
  "name": "Custom Footer",
  "settings": [
    {
      "type": "header",
      "content": "Layout Settings"
    },
    {
      "type": "select",
      "id": "footer_width",
      "label": "Footer Width",
      "options": [
        {
          "value": "full",
          "label": "Full Width"
        },
        {
          "value": "container",
          "label": "Container Width"
        }
      ],
      "default": "full"
    },
    // Additional settings...
  ]
}
```

### CSS Variable System

The widget uses CSS variables to apply customization options:

```css
.footer-custom {
  --footer-bg-color: {{ section.settings.bg_color }};
  --footer-text-color: {{ section.settings.text_color }};
  --footer-heading-color: {{ section.settings.heading_color }};
  --footer-link-color: {{ section.settings.link_color }};
  --footer-link-hover-color: {{ section.settings.link_hover_color }};
  --footer-border-color: {{ section.settings.border_color }};
  --footer-border-width: {{ section.settings.border_width }}px;
  --footer-top-padding: {{ section.settings.top_padding }}px;
  --footer-bottom-padding: {{ section.settings.bottom_padding }}px;
  /* Additional variables... */
}
```

### JavaScript Mobile Functionality

The mobile accordion functionality is implemented with vanilla JavaScript:

```javascript
document.addEventListener('DOMContentLoaded', function() {
  // Mobile accordion functionality
  const toggles = document.querySelectorAll('.footer-custom-mobile-toggle');
  
  toggles.forEach(toggle => {
    toggle.addEventListener('click', function() {
      const menu = this.closest('.footer-custom-column').querySelector('.footer-custom-menu');
      this.classList.toggle('active');
      menu.classList.toggle('active');
    });
  });
});
```

## Extending the Widget

### Adding New Customization Options

To add new customization options:

1. Add the setting to the section schema in `footer-custom.liquid` and `footer-custom-all-in-one.liquid`:

```liquid
{
  "type": "color",
  "id": "new_color_option",
  "label": "New Color Option",
  "default": "#ffffff"
}
```

2. Add the corresponding CSS variable in the section:

```liquid
--new-css-variable: {{ section.settings.new_color_option }};
```

3. Use the variable in your CSS:

```css
.selector {
  property: var(--new-css-variable);
}
```

### Adding New Columns

To add a new column type:

1. Duplicate an existing column block in the section schema
2. Update the type, name, and settings
3. Add the corresponding Liquid template code in the section

### Modifying Responsive Behavior

The responsive behavior is controlled by media queries in `custom-footer.css`:

```css
@media (max-width: 1200px) {
  .footer-custom-column {
    width: 25%;
  }
}

@media (max-width: 991px) {
  .footer-custom-column {
    width: 33.333%;
  }
}

/* Additional breakpoints... */
```

To modify breakpoints or behavior, update these media queries.

## Atelier Theme Integration

### Theme CSS Variables

The Atelier theme uses a CSS variable system. This footer widget integrates with it:

```css
font-family: var(--body-font-family, sans-serif);
```

### Performance Optimizations

1. **Deferred JavaScript Loading**:
   ```html
   <script src="{{ 'custom-footer.js' | asset_url }}" defer></script>
   ```

2. **Conditional Loading**:
   ```liquid
   {% if section.settings.show_feature %}
     <!-- Feature code -->
   {% endif %}
   ```

3. **Image Optimization**:
   ```liquid
   {% if section.settings.bg_image != blank %}
     <div class="footer-custom-bg" style="background-image: url('{{ section.settings.bg_image | img_url: '2000x' }}');"></div>
   {% endif %}
   ```

## Troubleshooting Common Issues

### Background Image Not Displaying

Check that:
- The image URL is valid
- The overlay opacity isn't too high
- The z-index values are correct

### Mobile Accordion Not Working

Check that:
- The JavaScript is loading properly
- The toggle selectors match the HTML structure
- There are no JavaScript errors in the console

### CSS Conflicts with Theme

If there are styling conflicts:
- Add more specific selectors to the custom footer CSS
- Use !important for critical styles (sparingly)
- Check for conflicting theme styles in the browser inspector

## Performance Considerations

- The widget is optimized to load efficiently
- CSS and JavaScript are kept minimal
- Background images use Shopify's image optimization

## Advanced Customization Examples

### Custom Column Layout

```liquid
<div class="footer-custom-column custom-layout">
  <!-- Custom column content -->
</div>
```

### Integration with Other Sections

```liquid
<div class="footer-custom-integration">
  {% section 'other-section' %}
</div>
```

### Dynamic Content Loading

```liquid
<div class="footer-custom-dynamic">
  {% for item in collection.products limit: 3 %}
    <!-- Product display -->
  {% endfor %}
</div>
```

## Atelier Theme Compatibility Notes

The Atelier theme v2.1.6 has specific requirements:

1. Sections must follow a specific structure
2. Theme settings are accessed in a particular way
3. Responsive behavior follows theme conventions
4. JavaScript should be deferred to avoid blocking

This custom footer widget has been developed with these requirements in mind.
