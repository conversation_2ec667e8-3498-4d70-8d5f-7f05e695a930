/**
 * Custom Footer JavaScript for Outdoor Studio
 * Compatible with Atelier Theme v2.1.6
 *
 * This script handles the mobile functionality of the footer:
 * - Collapsible column titles for mobile view
 * - Adds appropriate CSS variables from section settings
 */

document.addEventListener('DOMContentLoaded', function() {
  const customFooter = document.querySelector('.custom-footer');
  
  if (!customFooter) return;
  
  // Apply dynamic styles from section settings
  applyDynamicStyles();
  
  // Set up mobile accordion functionality
  setupMobileAccordion();
  
  /**
   * Apply styles based on section settings
   */
  function applyDynamicStyles() {
    // Get the section settings from data attributes
    const sectionId = customFooter.closest('[id^="shopify-section-"]')?.id;
    if (!sectionId) return;
    
    const section = document.getElementById(sectionId);
    
    // Background image
    const bgImage = section.querySelector('.custom-footer').dataset.backgroundImage;
    if (bgImage) {
      document.documentElement.style.setProperty('--footer-bg-image', `url(${bgImage})`);
      document.querySelector('.custom-footer::before').style.backgroundImage = `url(${bgImage})`;
    }
    
    // Background color
    const bgColor = section.querySelector('.custom-footer').dataset.backgroundColor;
    if (bgColor) {
      document.documentElement.style.setProperty('--footer-bg', bgColor);
    }
  }
  
  /**
   * Set up accordion functionality for mobile view
   */
  function setupMobileAccordion() {
    // Only apply on mobile screens
    const isMobile = window.innerWidth < 768;
    
    if (isMobile) {
      const columnTitles = customFooter.querySelectorAll('.custom-footer__column-title');
      
      columnTitles.forEach(title => {
        // Skip the column containing contact info
        if (title.nextElementSibling && title.nextElementSibling.classList.contains('custom-footer__contact')) {
          return;
        }
        
        // Add toggle functionality
        title.addEventListener('click', function() {
          const menu = this.nextElementSibling;
          if (!menu || !menu.classList.contains('custom-footer__menu')) return;
          
          // Toggle active class
          this.classList.toggle('active');
          
          // Toggle menu visibility
          if (menu.style.maxHeight) {
            menu.style.maxHeight = null;
            menu.style.opacity = '0';
            menu.classList.remove('active');
          } else {
            menu.style.maxHeight = menu.scrollHeight + 'px';
            menu.style.opacity = '1';
            menu.classList.add('active');
          }
        });
        
        // Add styling for clickable titles
        title.style.cursor = 'pointer';
        title.style.position = 'relative';
        
        // Add "+" icon using ::after pseudo-element
        title.classList.add('has-dropdown');
      });
      
      // Add CSS for dropdown icons
      addDropdownStyles();
    }
  }
  
  /**
   * Add CSS styles for dropdown functionality
   */
  function addDropdownStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .custom-footer__column-title.has-dropdown {
        padding-right: 30px;
      }
      
      .custom-footer__column-title.has-dropdown::after {
        content: '+';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        font-size: 1.2rem;
        transition: transform 0.3s ease;
        width: 24px;
        height: 24px;
        line-height: 22px;
        text-align: center;
        border-radius: 50%;
        background-color: rgba(0,0,0,0.05);
      }
      
      .custom-footer__column-title.has-dropdown.active::after {
        content: '-';
      }
      
      @media screen and (max-width: 767px) {
        .custom-footer__menu {
          max-height: 0;
          overflow: hidden;
          opacity: 0;
          transition: max-height 0.5s ease, opacity 0.3s ease;
          padding-top: 0;
        }
        
        .custom-footer__menu.active {
          padding-top: 10px;
        }
        
        /* Make sure the social links in the contact column are always visible */
        .custom-footer__social-title,
        .custom-footer__social-links {
          display: block !important;
          opacity: 1 !important;
          max-height: none !important;
          overflow: visible !important;
        }
      }
    `;
    document.head.appendChild(style);
  }
  
  // Update accordion on window resize
  window.addEventListener('resize', function() {
    if (window.innerWidth >= 768) {
      // Reset all menus on desktop view
      const menus = customFooter.querySelectorAll('.custom-footer__menu');
      menus.forEach(menu => {
        menu.style.maxHeight = '';
        menu.style.opacity = '';
      });
      
      const titles = customFooter.querySelectorAll('.custom-footer__column-title');
      titles.forEach(title => {
        title.classList.remove('active');
      });
    } else {
      // Re-initialize for mobile
      setupMobileAccordion();
    }
  });
});
