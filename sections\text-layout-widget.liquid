{% comment %}
  Text Layout Widget for Atelier Theme v2.1.6
  Multi-line text layout with individual styling options
{% endcomment %}

<div class="text-layout-widget" 
     id="text-layout-{{ section.id }}">
  
  <div class="text-layout-container">
    {% for block in section.blocks %}
      <div class="text-layout-block" 
           data-block-id="{{ block.id }}"
           data-alignment="{{ block.settings.text_alignment }}"
           {{ block.shopify_attributes }}>
        <{{ block.settings.text_tag }} class="text-layout-content">
          {{ block.settings.text_content }}
        </{{ block.settings.text_tag }}>
      </div>
    {% endfor %}
  </div>
</div>

<style>
  #text-layout-{{ section.id }} {
    --section-bg-color: {{ section.settings.section_bg_color }};
    --section-bg-gradient: {{ section.settings.section_bg_gradient }};
    --section-padding-top: {{ section.settings.section_padding_top }}px;
    --section-padding-bottom: {{ section.settings.section_padding_bottom }}px;
    --section-max-width: {{ section.settings.section_max_width }}px;
    --text-alignment: {{ section.settings.text_alignment }};
  }
  
  {% for block in section.blocks %}
    #text-layout-{{ section.id }} [data-block-id="{{ block.id }}"] .text-layout-content {
      font-size: {{ block.settings.text_size }}px;
      font-weight: {{ block.settings.text_weight }};
      color: {{ block.settings.text_color }};
      line-height: {{ block.settings.line_height }};
      letter-spacing: {{ block.settings.letter_spacing }}em;
      margin-bottom: {{ block.settings.margin_bottom }}px;
      
      {% if block.settings.text_italic %}
        font-style: italic;
      {% endif %}
      
      {% if block.settings.text_gradient != blank %}
        background: {{ block.settings.text_gradient }};
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      {% endif %}
      
      {% if block.settings.text_shadow != blank %}
        text-shadow: {{ block.settings.text_shadow }};
      {% endif %}
      
      {% if block.settings.text_stroke_width > 0 %}
        -webkit-text-stroke: {{ block.settings.text_stroke_width }}px {{ block.settings.text_stroke_color }};
      {% endif %}
    }
    
    #text-layout-{{ section.id }} [data-block-id="{{ block.id }}"] {
      text-align: {{ block.settings.text_alignment }};
    }
  {% endfor %}
</style>

{% schema %}
{
  "name": "Text Layout Widget",
  "tag": "section",
  "class": "text-layout-section",
  "settings": [
    {
      "type": "header",
      "content": "Layout Settings"
    },
    {
      "type": "range",
      "id": "section_padding_top",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "label": "Top Padding",
      "default": 80
    },
    {
      "type": "range",
      "id": "section_padding_bottom",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "label": "Bottom Padding",
      "default": 80
    },
    {
      "type": "range",
      "id": "section_max_width",
      "min": 800,
      "max": 1400,
      "step": 50,
      "unit": "px",
      "label": "Maximum Width",
      "default": 1200
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Default Text Alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "Background"
    },
    {
      "type": "color",
      "id": "section_bg_color",
      "label": "Background Color",
      "default": "#f5f5f5"
    },
    {
      "type": "color_background",
      "id": "section_bg_gradient",
      "label": "Background Gradient"
    }
  ],
  "blocks": [
    {
      "type": "text_block",
      "name": "Text Block",
      "settings": [
        {
          "type": "textarea",
          "id": "text_content",
          "label": "Text Content",
          "default": "Your Text Here"
        },
        {
          "type": "select",
          "id": "text_tag",
          "label": "HTML Tag",
          "options": [
            {
              "value": "h1",
              "label": "Heading 1"
            },
            {
              "value": "h2",
              "label": "Heading 2"
            },
            {
              "value": "h3",
              "label": "Heading 3"
            },
            {
              "value": "h4",
              "label": "Heading 4"
            },
            {
              "value": "p",
              "label": "Paragraph"
            },
            {
              "value": "span",
              "label": "Span"
            }
          ],
          "default": "h2"
        },
        {
          "type": "header",
          "content": "Typography"
        },
        {
          "type": "range",
          "id": "text_size",
          "min": 12,
          "max": 120,
          "step": 2,
          "unit": "px",
          "label": "Font Size",
          "default": 48
        },
        {
          "type": "select",
          "id": "text_weight",
          "label": "Font Weight",
          "options": [
            {
              "value": "100",
              "label": "Thin"
            },
            {
              "value": "200",
              "label": "Extra Light"
            },
            {
              "value": "300",
              "label": "Light"
            },
            {
              "value": "400",
              "label": "Normal"
            },
            {
              "value": "500",
              "label": "Medium"
            },
            {
              "value": "600",
              "label": "Semi Bold"
            },
            {
              "value": "700",
              "label": "Bold"
            },
            {
              "value": "800",
              "label": "Extra Bold"
            },
            {
              "value": "900",
              "label": "Black"
            }
          ],
          "default": "400"
        },
        {
          "type": "checkbox",
          "id": "text_italic",
          "label": "Italic Text",
          "default": false
        },
        {
          "type": "range",
          "id": "line_height",
          "min": 0.8,
          "max": 2.5,
          "step": 0.1,
          "label": "Line Height",
          "default": 1.2
        },
        {
          "type": "range",
          "id": "letter_spacing",
          "min": -0.1,
          "max": 0.5,
          "step": 0.01,
          "label": "Letter Spacing",
          "default": 0.02
        },
        {
          "type": "header",
          "content": "Positioning"
        },
        {
          "type": "select",
          "id": "text_alignment",
          "label": "Text Alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ],
          "default": "center"
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "px",
          "label": "Bottom Margin",
          "default": 20
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text Color",
          "default": "#8B7355"
        },
        {
          "type": "color_background",
          "id": "text_gradient",
          "label": "Text Gradient (overrides color)"
        },
        {
          "type": "header",
          "content": "Text Effects"
        },
        {
          "type": "text",
          "id": "text_shadow",
          "label": "Text Shadow (CSS format)",
          "info": "Example: 2px 2px 4px rgba(0,0,0,0.3)",
          "placeholder": "2px 2px 4px rgba(0,0,0,0.3)"
        },
        {
          "type": "range",
          "id": "text_stroke_width",
          "min": 0,
          "max": 5,
          "step": 0.5,
          "unit": "px",
          "label": "Text Stroke Width",
          "default": 0
        },
        {
          "type": "color",
          "id": "text_stroke_color",
          "label": "Text Stroke Color",
          "default": "#000000"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Text Layout Widget",
      "blocks": [
        {
          "type": "text_block",
          "settings": {
            "text_content": "At The Heart",
            "text_tag": "h1",
            "text_size": 72,
            "text_weight": "300",
            "text_italic": true,
            "text_color": "#8B7355",
            "text_alignment": "left",
            "margin_bottom": 10
          }
        },
        {
          "type": "text_block",
          "settings": {
            "text_content": "of Every Outdoor",
            "text_tag": "h1",
            "text_size": 72,
            "text_weight": "300",
            "text_italic": true,
            "text_color": "#8B7355",
            "text_alignment": "center",
            "margin_bottom": 10
          }
        },
        {
          "type": "text_block",
          "settings": {
            "text_content": "Transformation",
            "text_tag": "h1",
            "text_size": 72,
            "text_weight": "300",
            "text_italic": true,
            "text_color": "#8B7355",
            "text_alignment": "left",
            "margin_bottom": 40
          }
        },
        {
          "type": "text_block",
          "settings": {
            "text_content": "With a passion for design and quality, we bring your outdoor vision to life, combining beauty and practicality.",
            "text_tag": "p",
            "text_size": 16,
            "text_weight": "400",
            "text_italic": false,
            "text_color": "#999999",
            "text_alignment": "left",
            "margin_bottom": 30
          }
        },
        {
          "type": "text_block",
          "settings": {
            "text_content": "A Legacy of Craftsmanship,",
            "text_tag": "h3",
            "text_size": 32,
            "text_weight": "300",
            "text_italic": true,
            "text_color": "#CCCCCC",
            "text_alignment": "right",
            "margin_bottom": 10
          }
        },
        {
          "type": "text_block",
          "settings": {
            "text_content": "Built for Today's Outdoors",
            "text_tag": "h3",
            "text_size": 32,
            "text_weight": "300",
            "text_italic": true,
            "text_color": "#CCCCCC",
            "text_alignment": "right",
            "margin_bottom": 0
          }
        }
      ]
    }
  ]
}
{% endschema %}