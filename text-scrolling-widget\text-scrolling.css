/* Text Scrolling Widget Styles */
.text-scrolling-widget {
  width: 100%;
  height: var(--section-height, 200px);
  background: var(--section-bg-color, #ffffff);
  background: var(--section-bg-gradient, var(--section-bg-color, #ffffff));
  padding-top: var(--section-padding-top, 40px);
  padding-bottom: var(--section-padding-bottom, 40px);
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
}

.text-scrolling-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.text-scrolling-track {
  display: flex;
  align-items: center;
  height: 100%;
  white-space: nowrap;
  position: absolute;
  top: 0;
  left: 0;
  animation-duration: var(--scroll-speed, 20s);
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-fill-mode: forwards;
}

/* Animation Classes */
.text-scrolling-track.animate-left-to-right {
  animation-name: scrollLeftToRight;
}

.text-scrolling-track.animate-right-to-left {
  animation-name: scrollRightToLeft;
}

.text-scrolling-track.paused {
  animation-play-state: paused;
}

/* Keyframe Animations */
@keyframes scrollLeftToRight {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes scrollRightToLeft {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* Text Items */
.text-scrolling-item {
  display: inline-block;
  margin-right: 100px;
  flex-shrink: 0;
}

.text-scrolling-content {
  font-size: var(--text-size, 48px);
  font-weight: var(--text-weight, 600);
  line-height: 1.2;
  display: inline-block;
  font-family: var(--font-heading-family, inherit);
  letter-spacing: 0.02em;
  transition: all 0.3s ease;
}

/* Hover Effects */
.text-scrolling-widget[data-animation-trigger="on-hover"]:hover .text-scrolling-track {
  animation-play-state: running;
}

.text-scrolling-widget[data-animation-trigger="on-hover"] .text-scrolling-track {
  animation-play-state: paused;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .text-scrolling-widget {
    height: calc(var(--section-height, 200px) * 0.7);
  }
  
  .text-scrolling-content {
    font-size: calc(var(--text-size, 48px) * 0.6);
  }
  
  .text-scrolling-item {
    margin-right: 50px;
  }
}

@media screen and (max-width: 480px) {
  .text-scrolling-widget {
    height: calc(var(--section-height, 200px) * 0.5);
  }
  
  .text-scrolling-content {
    font-size: calc(var(--text-size, 48px) * 0.4);
  }
  
  .text-scrolling-item {
    margin-right: 30px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .text-scrolling-track {
    animation: none;
    position: static;
    transform: none;
  }
  
  .text-scrolling-widget {
    justify-content: center;
    text-align: center;
  }
  
  .text-scrolling-item {
    display: block;
    margin: 10px 0;
  }
}

/* Performance Optimizations */
.text-scrolling-track {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

.text-scrolling-content {
  will-change: color, background;
}

/* Loading State */
.text-scrolling-widget.loading .text-scrolling-track {
  opacity: 0;
  animation-play-state: paused;
}

.text-scrolling-widget.loaded .text-scrolling-track {
  opacity: 1;
  transition: opacity 0.5s ease;
}

/* Custom Gradient Text Support */
.text-scrolling-content[style*="background"] {
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  color: transparent !important;
}