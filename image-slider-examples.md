# Image Slider Widget - Usage Examples

## Example 1: Contact Us Page Hero Slider

Perfect for showcasing your business on a contact page.

### Configuration:
- **Height**: 500px desktop, 350px mobile
- **Slides per view**: 1
- **Auto-play**: 6 seconds
- **Border radius**: 16px
- **Navigation**: Enabled
- **Pagination**: Enabled

### Sample Slides:
1. **Office Location**
   - Image: Your office building or storefront
   - Title: "Visit Our Showroom"
   - Description: "Located in the heart of downtown, our showroom features the latest outdoor furniture collections."
   - Link: `/pages/location`

2. **Team Photo**
   - Image: Professional team photo
   - Title: "Meet Our Expert Team"
   - Description: "Our experienced designers are ready to help transform your outdoor space."
   - Link: `/pages/about`

3. **Contact Methods**
   - Image: Phone/email graphic or consultation photo
   - Title: "Get In Touch Today"
   - Description: "Call us at (************* or schedule a free consultation online."
   - Link: `/pages/consultation`

## Example 2: Product Showcase Slider

Great for displaying multiple products or categories.

### Configuration:
- **Height**: 400px desktop, 300px mobile
- **Slides per view**: 3 desktop, 1 mobile
- **Auto-play**: 5 seconds
- **Gap between slides**: 25px
- **Border radius**: 12px
- **Content overlay**: 60% opacity

### Sample Slides:
1. **Outdoor Dining Sets**
   - Image: Beautiful dining set in garden setting
   - Title: "Dining Collections"
   - Description: "Create memorable outdoor dining experiences"
   - Link: `/collections/dining`

2. **Lounge Furniture**
   - Image: Comfortable outdoor seating area
   - Title: "Lounge & Seating"
   - Description: "Relax in style with our premium seating"
   - Link: `/collections/seating`

3. **Garden Accessories**
   - Image: Decorative planters and accessories
   - Title: "Garden Decor"
   - Description: "Complete your space with beautiful accessories"
   - Link: `/collections/accessories`

## Example 3: Customer Testimonial Slider

Showcase customer reviews and success stories.

### Configuration:
- **Height**: 350px desktop, 280px mobile
- **Slides per view**: 2 desktop, 1 mobile
- **Auto-play**: 8 seconds
- **Border radius**: 20px
- **Content overlay**: 85% opacity
- **Navigation**: Disabled (clean look)

### Sample Slides:
1. **Customer Project 1**
   - Image: Before/after transformation photo
   - Title: "Amazing Transformation"
   - Description: "The team exceeded our expectations. Our backyard is now our favorite room!" - Sarah M.

2. **Customer Project 2**
   - Image: Happy family in their new outdoor space
   - Title: "Perfect for Entertaining"
   - Description: "We've hosted three parties already this summer. Everyone loves our new setup!" - Mike R.

3. **Customer Project 3**
   - Image: Cozy outdoor reading nook
   - Title: "My Personal Oasis"
   - Description: "This is where I start every morning with coffee. Pure bliss!" - Jennifer L.

## Example 4: Process/Services Slider

Show your work process or service offerings.

### Configuration:
- **Height**: 450px desktop, 320px mobile
- **Slides per view**: 1
- **Auto-play**: 7 seconds
- **Border radius**: 8px
- **Navigation**: Enabled
- **Pagination**: Enabled with custom colors

### Sample Slides:
1. **Consultation**
   - Image: Designer meeting with clients
   - Title: "1. Free Consultation"
   - Description: "We start with understanding your vision, space, and budget requirements."

2. **Design**
   - Image: 3D design renderings or sketches
   - Title: "2. Custom Design"
   - Description: "Our team creates detailed plans tailored to your specific needs and style."

3. **Installation**
   - Image: Professional installation team at work
   - Title: "3. Expert Installation"
   - Description: "Our certified installers ensure everything is set up perfectly and safely."

4. **Enjoyment**
   - Image: Finished outdoor space being enjoyed
   - Title: "4. Enjoy Your Space"
   - Description: "Relax and enjoy your beautiful new outdoor living area for years to come."

## Example 5: Seasonal Collections Slider

Perfect for highlighting seasonal products or promotions.

### Configuration:
- **Height**: 380px desktop, 290px mobile
- **Slides per view**: 2 desktop, 1 mobile
- **Auto-play**: 4 seconds
- **Gap between slides**: 20px
- **Border radius**: 15px

### Sample Slides:
1. **Spring Collection**
   - Image: Fresh spring outdoor setup
   - Title: "Spring Refresh"
   - Description: "New arrivals perfect for spring entertaining"
   - Link: `/collections/spring`

2. **Summer Essentials**
   - Image: Pool/patio summer scene
   - Title: "Summer Essentials"
   - Description: "Beat the heat with our cooling solutions"
   - Link: `/collections/summer`

3. **Fall Comfort**
   - Image: Cozy fall outdoor setting
   - Title: "Fall Comfort"
   - Description: "Extend your outdoor season with warmth"
   - Link: `/collections/fall`

4. **Winter Storage**
   - Image: Covered furniture or storage solutions
   - Title: "Winter Protection"
   - Description: "Protect your investment year-round"
   - Link: `/collections/covers`

## Color Scheme Examples

### Modern Minimalist
- **Navigation**: #2c2c2c with white background
- **Pagination**: Light gray inactive, dark gray active
- **Content text**: Pure white
- **Section background**: #f8f9fa

### Warm & Inviting
- **Navigation**: #8B7355 with cream background
- **Pagination**: Warm beige inactive, brown active
- **Content text**: Cream white
- **Section background**: #f9f8f6

### Bold & Contemporary
- **Navigation**: #1a1a1a with bright white background
- **Pagination**: Medium gray inactive, black active
- **Content text**: White
- **Section background**: Pure white

## Mobile Optimization Tips

1. **Single Slide View**: Always use 1 slide per view on mobile for better readability
2. **Larger Text**: Ensure titles are at least 18px on mobile
3. **Touch Targets**: Navigation elements should be at least 44px for easy tapping
4. **Reduced Height**: Use 60-70% of desktop height for mobile
5. **Simplified Content**: Keep mobile descriptions shorter and more concise

## Performance Best Practices

1. **Image Optimization**: Use WebP format when possible
2. **Lazy Loading**: The widget automatically implements lazy loading
3. **Slide Limit**: Keep to 8 slides maximum for best performance
4. **Auto-play Timing**: Don't go below 3 seconds for auto-play
5. **Preload Critical**: Consider preloading the first slide image

These examples should give you a solid foundation for implementing the image slider widget effectively across different sections of your Shopify store.
