/* Text Layout Widget Styles */
.text-layout-widget {
  width: 100%;
  background: var(--section-bg-color, #f5f5f5);
  background: var(--section-bg-gradient, var(--section-bg-color, #f5f5f5));
  padding-top: var(--section-padding-top, 80px);
  padding-bottom: var(--section-padding-bottom, 80px);
  position: relative;
}

.text-layout-container {
  max-width: var(--section-max-width, 1200px);
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
}

.text-layout-block {
  position: relative;
  width: 100%;
}

.text-layout-content {
  font-family: var(--font-heading-family, inherit);
  margin: 0;
  padding: 0;
  transition: all 0.3s ease;
  word-wrap: break-word;
  hyphens: auto;
}

/* Responsive Typography */
@media screen and (max-width: 1024px) {
  .text-layout-container {
    padding: 0 30px;
  }
  
  .text-layout-content {
    font-size: calc(var(--responsive-font-size, 1em) * 0.9) !important;
  }
}

@media screen and (max-width: 768px) {
  .text-layout-container {
    padding: 0 20px;
  }
  
  .text-layout-content {
    font-size: calc(var(--responsive-font-size, 1em) * 0.7) !important;
  }
  
  .text-layout-block[data-alignment="left"],
  .text-layout-block[data-alignment="right"] {
    text-align: center !important;
  }
}

@media screen and (max-width: 480px) {
  .text-layout-container {
    padding: 0 15px;
  }
  
  .text-layout-content {
    font-size: calc(var(--responsive-font-size, 1em) * 0.6) !important;
  }
}

/* Text Alignment Classes */
.text-layout-block[data-alignment="left"] {
  text-align: left;
}

.text-layout-block[data-alignment="center"] {
  text-align: center;
}

.text-layout-block[data-alignment="right"] {
  text-align: right;
}

/* Custom Gradient Text Support */
.text-layout-content[style*="background"] {
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  color: transparent !important;
}

/* Hover Effects */
.text-layout-content:hover {
  transform: translateY(-2px);
}

/* Animation for loading */
.text-layout-block {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards;
}

.text-layout-block:nth-child(1) { animation-delay: 0.1s; }
.text-layout-block:nth-child(2) { animation-delay: 0.2s; }
.text-layout-block:nth-child(3) { animation-delay: 0.3s; }
.text-layout-block:nth-child(4) { animation-delay: 0.4s; }
.text-layout-block:nth-child(5) { animation-delay: 0.5s; }
.text-layout-block:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .text-layout-block {
    animation: none;
    opacity: 1;
    transform: none;
  }
  
  .text-layout-content:hover {
    transform: none;
  }
}

/* Print Styles */
@media print {
  .text-layout-widget {
    background: white !important;
    padding: 20px 0 !important;
  }
  
  .text-layout-content {
    color: black !important;
    text-shadow: none !important;
    -webkit-text-stroke: none !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .text-layout-content {
    text-shadow: none !important;
    -webkit-text-stroke: none !important;
  }
}

/* Focus States for Accessibility */
.text-layout-content:focus {
  outline: 2px solid #007cba;
  outline-offset: 2px;
}

/* Selection Styles */
.text-layout-content::selection {
  background: rgba(0, 124, 186, 0.2);
}

.text-layout-content::-moz-selection {
  background: rgba(0, 124, 186, 0.2);
}