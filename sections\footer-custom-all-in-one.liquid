{% comment %}
  Custom Footer Widget for Atelier Theme v2.1.6
  Developed for Outdoor Studio - All-in-one Version
  This version includes inline CSS and JavaScript for easier implementation
{% endcomment %}

<div class="custom-footer footer-width--{{ section.settings.footer_width }} heading-size--{{ section.settings.heading_size }} text-size--{{ section.settings.text_size }}"
     data-background-color="{{ section.settings.background_color }}" 
     {% if section.settings.background_image %}
     data-background-image="{{ section.settings.background_image | img_url: 'master' }}"
     style="background-color: {{ section.settings.background_color }}; background-image: url({{ section.settings.background_image | img_url: '2000x' }}); padding-top: {{ section.settings.footer_padding_top }}px; padding-bottom: {{ section.settings.footer_padding_bottom }}px; {% if section.settings.show_top_border %}border-top: {{ section.settings.border_width }}px solid {{ section.settings.border_color }};{% else %}border-top: none;{% endif %}"
     data-overlay-opacity="{{ section.settings.background_overlay_opacity | divided_by: 100.0 }}"
     {% else %}
     style="background-color: {{ section.settings.background_color }}; padding-top: {{ section.settings.footer_padding_top }}px; padding-bottom: {{ section.settings.footer_padding_bottom }}px; {% if section.settings.show_top_border %}border-top: {{ section.settings.border_width }}px solid {{ section.settings.border_color }};{% else %}border-top: none;{% endif %}"
     {% endif %}>
     
{% if section.settings.background_image %}
<style>
  .custom-footer::before {
    background-color: {{ section.settings.background_color | color_modify: 'alpha', section.settings.background_overlay_opacity | divided_by: 100.0 }};
  }
</style>
{% endif %}

<style>
  .custom-footer__column-title {
    color: {{ section.settings.heading_color }};
  }
  
  .custom-footer__menu-item a, 
  .custom-footer__contact p {
    color: {{ section.settings.text_color }};
  }
  
  .custom-footer__menu-item a {
    color: {{ section.settings.link_color }};
  }
  
  .custom-footer__menu-item a:hover {
    color: {{ section.settings.link_hover_color }};
  }
  
  .custom-footer__bottom {
    background-color: {{ section.settings.bottom_background_color }};
  }
  
  /* Custom Icon Styles */
  .custom-footer__social-links {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;
  }
  
  .custom-footer__social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: auto;
    height: auto;
    transition: all 0.3s ease;
  }
  
  .custom-footer__social-link.custom-icon {
    background: transparent;
    border-radius: 0;
  }
  
  .custom-footer__social-link img {
    max-width: 100%;
    height: auto;
    transition: all 0.3s ease;
  }
  
  /* Hover Effects */
  .hover-effect--grow .custom-footer__social-link:hover img {
    transform: scale(1.2);
  }
  
  .hover-effect--fade .custom-footer__social-link:hover img {
    opacity: 0.7;
  }
  
  .hover-effect--rotate .custom-footer__social-link:hover img {
    transform: rotate(10deg);
  }
</style>
  
  {% if section.settings.background_image %}
  <style>
    .custom-footer::before {
      background-image: url({{ section.settings.background_image | img_url: 'master' }});
    }
  </style>
  {% endif %}
  
  <div class="custom-footer__container">
    {%- if section.settings.show_columns -%}
      <div class="custom-footer__columns">
        <div class="custom-footer__column">
          <h3 class="custom-footer__column-title">{{ section.settings.column1_title }}</h3>
          <ul class="custom-footer__menu">
            {%- for link in linklists[section.settings.column1_menu].links -%}
              <li class="custom-footer__menu-item">
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {%- endfor -%}
          </ul>
        </div>
        
        <div class="custom-footer__column">
          <h3 class="custom-footer__column-title">{{ section.settings.column2_title }}</h3>
          <ul class="custom-footer__menu">
            {%- for link in linklists[section.settings.column2_menu].links -%}
              <li class="custom-footer__menu-item">
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {%- endfor -%}
          </ul>
        </div>
        
        <div class="custom-footer__column">
          <h3 class="custom-footer__column-title">{{ section.settings.column3_title }}</h3>
          <ul class="custom-footer__menu">
            {%- for link in linklists[section.settings.column3_menu].links -%}
              <li class="custom-footer__menu-item">
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {%- endfor -%}
          </ul>
        </div>
        
        <div class="custom-footer__column">
          <h3 class="custom-footer__column-title">{{ section.settings.column4_title }}</h3>
          <ul class="custom-footer__menu">
            {%- for link in linklists[section.settings.column4_menu].links -%}
              <li class="custom-footer__menu-item">
                <a href="{{ link.url }}">{{ link.title }}</a>
              </li>
            {%- endfor -%}
          </ul>
        </div>

        <div class="custom-footer__column">
          <h3 class="custom-footer__column-title">{{ section.settings.column5_title }}</h3>
          <div class="custom-footer__contact">
            {%- if section.settings.address != blank -%}
              <p class="custom-footer__address">{{ section.settings.address }}</p>
            {%- endif -%}
            
            {%- if section.settings.phone1 != blank or section.settings.phone2 != blank -%}
              <p class="custom-footer__phone">
                {%- if section.settings.phone1 != blank -%}
                  <span>{{ section.settings.phone1 }}</span>
                {%- endif -%}
                {%- if section.settings.phone1 != blank and section.settings.phone2 != blank -%}, {% endif -%}
                {%- if section.settings.phone2 != blank -%}
                  <span>{{ section.settings.phone2 }}</span>
                {%- endif -%}
              </p>
            {%- endif -%}
            
            {%- if section.settings.email != blank -%}
              <p class="custom-footer__email">{{ section.settings.email }}</p>
            {%- endif -%}
          </div>
          
          <h3 class="custom-footer__social-title" style="margin-top: 20px;">{{ section.settings.social_title }}</h3>
          <div class="custom-footer__social-links hover-effect--{{ section.settings.hover_effect }}">
            {% if section.settings.use_custom_icons %}
              {%- if section.settings.facebook_icon != blank and section.settings.facebook_link != blank -%}
                <a href="{{ section.settings.facebook_link }}" class="custom-footer__social-link custom-icon" target="_blank" rel="noopener" aria-label="Facebook">
                  <img src="{{ section.settings.facebook_icon | img_url: 'master' }}" alt="Facebook" width="{{ section.settings.icon_size }}" height="{{ section.settings.icon_size }}">
                </a>
              {%- endif -%}
              
              {%- if section.settings.twitter_icon != blank and section.settings.twitter_link != blank -%}
                <a href="{{ section.settings.twitter_link }}" class="custom-footer__social-link custom-icon" target="_blank" rel="noopener" aria-label="Twitter/X">
                  <img src="{{ section.settings.twitter_icon | img_url: 'master' }}" alt="Twitter/X" width="{{ section.settings.icon_size }}" height="{{ section.settings.icon_size }}">
                </a>
              {%- endif -%}
              
              {%- if section.settings.instagram_icon != blank and section.settings.instagram_link != blank -%}
                <a href="{{ section.settings.instagram_link }}" class="custom-footer__social-link custom-icon" target="_blank" rel="noopener" aria-label="Instagram">
                  <img src="{{ section.settings.instagram_icon | img_url: 'master' }}" alt="Instagram" width="{{ section.settings.icon_size }}" height="{{ section.settings.icon_size }}">
                </a>
              {%- endif -%}
              
              {%- if section.settings.pinterest_icon != blank and section.settings.pinterest_link != blank -%}
                <a href="{{ section.settings.pinterest_link }}" class="custom-footer__social-link custom-icon" target="_blank" rel="noopener" aria-label="Pinterest">
                  <img src="{{ section.settings.pinterest_icon | img_url: 'master' }}" alt="Pinterest" width="{{ section.settings.icon_size }}" height="{{ section.settings.icon_size }}">
                </a>
              {%- endif -%}
              
              {%- if section.settings.youtube_icon != blank and section.settings.youtube_link != blank -%}
                <a href="{{ section.settings.youtube_link }}" class="custom-footer__social-link custom-icon" target="_blank" rel="noopener" aria-label="YouTube">
                  <img src="{{ section.settings.youtube_icon | img_url: 'master' }}" alt="YouTube" width="{{ section.settings.icon_size }}" height="{{ section.settings.icon_size }}">
                </a>
              {%- endif -%}
              
              {%- if section.settings.linkedin_icon != blank and section.settings.linkedin_link != blank -%}
                <a href="{{ section.settings.linkedin_link }}" class="custom-footer__social-link custom-icon" target="_blank" rel="noopener" aria-label="LinkedIn">
                  <img src="{{ section.settings.linkedin_icon | img_url: 'master' }}" alt="LinkedIn" width="{{ section.settings.icon_size }}" height="{{ section.settings.icon_size }}">
                </a>
              {%- endif -%}
            {% else %}
              {%- if settings.social_facebook_link != blank -%}
                <a href="{{ settings.social_facebook_link }}" class="custom-footer__social-link" target="_blank" rel="noopener" aria-label="Facebook">
                  {% include 'icon-facebook' %}
                </a>
              {%- endif -%}
              {%- if settings.social_twitter_link != blank -%}
                <a href="{{ settings.social_twitter_link }}" class="custom-footer__social-link" target="_blank" rel="noopener" aria-label="Twitter">
                  {% include 'icon-twitter' %}
                </a>
              {%- endif -%}
              {%- if settings.social_pinterest_link != blank -%}
                <a href="{{ settings.social_pinterest_link }}" class="custom-footer__social-link" target="_blank" rel="noopener" aria-label="Pinterest">
                  {% include 'icon-pinterest' %}
                </a>
              {%- endif -%}
              {%- if settings.social_instagram_link != blank -%}
                <a href="{{ settings.social_instagram_link }}" class="custom-footer__social-link" target="_blank" rel="noopener" aria-label="Instagram">
                  {% include 'icon-instagram' %}
                </a>
              {%- endif -%}
              {%- if settings.social_youtube_link != blank -%}
                <a href="{{ settings.social_youtube_link }}" class="custom-footer__social-link" target="_blank" rel="noopener" aria-label="YouTube">
                  {% include 'icon-youtube' %}
                </a>
              {%- endif -%}
              {%- if settings.social_linkedin_link != blank -%}
                <a href="{{ settings.social_linkedin_link }}" class="custom-footer__social-link" target="_blank" rel="noopener" aria-label="LinkedIn">
                  {% include 'icon-linkedin' %}
                </a>
              {%- endif -%}
            {% endif %}
          </div>
    </div>
  </div>

  <div class="custom-footer__bottom">
    <div class="custom-footer__container">
      <div class="custom-footer__copyright">
        {%- if section.settings.copyright_text != blank -%}
          {{ section.settings.copyright_text }}
        {%- else -%}
          &copy; {{ 'now' | date: '%Y' }} {{ shop.name | link_to: routes.root_url }}
        {%- endif -%}
      </div>
      
      <div class="custom-footer__bottom-links">
        {%- for link in linklists[section.settings.bottom_menu].links -%}
          <a href="{{ link.url }}" class="custom-footer__bottom-link">{{ link.title }}</a>
        {%- endfor -%}
      </div>
      
      {%- if section.settings.show_payment_icons and section.settings.payment_icon_enabled -%}
        <div class="custom-footer__payment">
          {% for type in shop.enabled_payment_types %}
            {{ type | payment_type_svg_tag: class: 'custom-footer__payment-icon' }}
          {% endfor %}
        </div>
      {%- endif -%}
      
      {%- if section.settings.show_designer_credit -%}
        <div class="custom-footer__designer-credit">
          <span>Crafted with</span> 
          <svg class="heart-icon" width="12" height="12" viewBox="0 0 24 24" fill="red">
            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
          </svg>
          <span>by {{ section.settings.designer_name }}</span>
        </div>
      {%- endif -%}
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Custom Footer",
  "settings": [
    {
      "type": "header",
      "content": "Layout Settings"
    },
    {
      "type": "checkbox",
      "id": "show_columns",
      "label": "Show Footer Columns",
      "default": true
    },
    {
      "type": "select",
      "id": "footer_width",
      "label": "Footer Width",
      "options": [
        {
          "value": "full_width",
          "label": "Full Width"
        },
        {
          "value": "container",
          "label": "Container Width"
        }
      ],
      "default": "container"
    },
    {
      "type": "range",
      "id": "footer_padding_top",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Top Padding",
      "default": 60
    },
    {
      "type": "range",
      "id": "footer_padding_bottom",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "Bottom Padding",
      "default": 20
    },
    {
      "type": "header",
      "content": "Footer Columns"
    },
    {
      "type": "text",
      "id": "column1_title",
      "label": "Column 1 Title",
      "default": "Our Collections"
    },
    {
      "type": "link_list",
      "id": "column1_menu",
      "label": "Column 1 Menu",
      "default": "footer"
    },
    {
      "type": "text",
      "id": "column2_title",
      "label": "Column 2 Title",
      "default": "Quick Links"
    },
    {
      "type": "link_list",
      "id": "column2_menu",
      "label": "Column 2 Menu"
    },
    {
      "type": "text",
      "id": "column3_title",
      "label": "Column 3 Title",
      "default": "Customer Service"
    },
    {
      "type": "link_list",
      "id": "column3_menu",
      "label": "Column 3 Menu"
    },
    {
      "type": "text",
      "id": "column4_title",
      "label": "Column 4 Title",
      "default": "Information"
    },
    {
      "type": "link_list",
      "id": "column4_menu",
      "label": "Column 4 Menu"
    },
    {
      "type": "text",
      "id": "column5_title",
      "label": "Column 5 Title",
      "default": "Outdoor Studio"
    },
    {
      "type": "textarea",
      "id": "address",
      "label": "Address",
      "default": "#10/1, Venkataswamy Road, Near Indian Express, Bengaluru, Karnataka, 560051"
    },
    {
      "type": "text",
      "id": "phone1",
      "label": "Phone Number 1",
      "default": "+91 7090520619"
    },
    {
      "type": "text",
      "id": "phone2",
      "label": "Phone Number 2",
      "default": "+91 9880931818"
    },
    {
      "type": "text",
      "id": "email",
      "label": "Email",
      "default": "<EMAIL>"
    },
    {
      "type": "header",
      "content": "Social Links"
    },
    {
      "type": "text",
      "id": "social_title",
      "label": "Social Title",
      "default": "Find Us"
    },
    {
      "type": "checkbox",
      "id": "use_custom_icons",
      "label": "Use Custom Icons",
      "default": false,
      "info": "Upload your own custom icons instead of using the default SVG icons"
    },
    {
      "type": "image_picker",
      "id": "facebook_icon",
      "label": "Facebook Icon",
      "info": "32 x 32px recommended (PNG or SVG)"
    },
    {
      "type": "url",
      "id": "facebook_link",
      "label": "Facebook URL"
    },
    {
      "type": "image_picker",
      "id": "twitter_icon",
      "label": "Twitter/X Icon",
      "info": "32 x 32px recommended (PNG or SVG)"
    },
    {
      "type": "url",
      "id": "twitter_link",
      "label": "Twitter/X URL"
    },
    {
      "type": "image_picker",
      "id": "instagram_icon",
      "label": "Instagram Icon",
      "info": "32 x 32px recommended (PNG or SVG)"
    },
    {
      "type": "url",
      "id": "instagram_link",
      "label": "Instagram URL"
    },
    {
      "type": "image_picker",
      "id": "pinterest_icon",
      "label": "Pinterest Icon",
      "info": "32 x 32px recommended (PNG or SVG)"
    },
    {
      "type": "url",
      "id": "pinterest_link",
      "label": "Pinterest URL"
    },
    {
      "type": "image_picker",
      "id": "youtube_icon",
      "label": "YouTube Icon",
      "info": "32 x 32px recommended (PNG or SVG)"
    },
    {
      "type": "url",
      "id": "youtube_link",
      "label": "YouTube URL"
    },
    {
      "type": "image_picker",
      "id": "linkedin_icon",
      "label": "LinkedIn Icon",
      "info": "32 x 32px recommended (PNG or SVG)"
    },
    {
      "type": "url",
      "id": "linkedin_link",
      "label": "LinkedIn URL"
    },
    {
      "type": "range",
      "id": "icon_size",
      "min": 16,
      "max": 64,
      "step": 4,
      "unit": "px",
      "label": "Icon Size",
      "default": 32
    },
    {
      "type": "select",
      "id": "hover_effect",
      "label": "Hover Effect",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "grow",
          "label": "Grow"
        },
        {
          "value": "fade",
          "label": "Fade"
        },
        {
          "value": "rotate",
          "label": "Rotate"
        }
      ],
      "default": "grow"
    },
    {
      "type": "header",
      "content": "Footer Bottom"
    },
    {
      "type": "text",
      "id": "copyright_text",
      "label": "Copyright Text",
      "default": "Copyright © 2025 Outdoor Studio | All rights reserved"
    },
    {
      "type": "link_list",
      "id": "bottom_menu",
      "label": "Bottom Menu",
      "default": "footer"
    },
    {
      "type": "checkbox",
      "id": "show_payment_icons",
      "label": "Show Payment Icons",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "payment_icon_enabled",
      "label": "Enable Payment Icons",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "show_designer_credit",
      "label": "Show Designer Credit",
      "default": true
    },
    {
      "type": "text",
      "id": "designer_name",
      "label": "Designer Name",
      "default": "Meela Monks"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "select",
      "id": "heading_size",
      "label": "Heading Size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "medium"
    },
    {
      "type": "select",
      "id": "text_size",
      "label": "Text Size",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "medium"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading Color",
      "default": "#222"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text Color",
      "default": "#555"
    },
    {
      "type": "color",
      "id": "link_color",
      "label": "Link Color",
      "default": "#555"
    },
    {
      "type": "color",
      "id": "link_hover_color",
      "label": "Link Hover Color",
      "default": "#000"
    },
    {
      "type": "header",
      "content": "Border Styling"
    },
    {
      "type": "checkbox",
      "id": "show_top_border",
      "label": "Show Top Border",
      "default": true
    },
    {
      "type": "color",
      "id": "border_color",
      "label": "Border Color",
      "default": "#e8e8e8"
    },
    {
      "type": "range",
      "id": "border_width",
      "min": 1,
      "max": 5,
      "step": 1,
      "unit": "px",
      "label": "Border Width",
      "default": 1
    },
    {
      "type": "header",
      "content": "Bottom Bar"
    },
    {
      "type": "color",
      "id": "bottom_background_color",
      "label": "Bottom Bar Background Color",
      "default": "rgba(0,0,0,0.03)"
    },
    {
      "type": "header",
      "content": "Background"
    },
    {
      "type": "image_picker",
      "id": "background_image",
      "label": "Background Image",
      "info": "Recommended size: 2000 x 800 pixels. Will be displayed as a full-width background."
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color or Overlay Color",
      "default": "#f9f8f6",
      "info": "Used as the main background color or as an overlay on top of the background image."
    },
    {
      "type": "range",
      "id": "background_overlay_opacity",
      "label": "Background Overlay Opacity",
      "min": 0,
      "max": 100,
      "step": 5,
      "default": 85,
      "info": "Controls the opacity of the overlay on top of the background image."
    }
  ],
  "presets": [
    {
      "name": "Custom Footer",
      "category": "Footer"
    }
  ]
}
{% endschema %}

<style>
/* 
  Custom Footer Styles for Outdoor Studio 
  Compatible with Atelier Theme v2.1.6
*/

.custom-footer {
  background-color: var(--footer-bg, #f9f8f6);
  color: var(--footer-color, #333);
  padding: 60px 0 0;
  font-family: var(--typebase-font-family);
  position: relative;
  width: 100%;
  border-top: 1px solid #e8e8e8;
}

/* Background image and overlay */
.custom-footer {
  background-size: cover;
  background-position: center bottom;
  background-repeat: no-repeat;
  position: relative;
}

.custom-footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  pointer-events: none;
}

.custom-footer__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

/* Columns layout */
.custom-footer__columns {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 50px;
}

.custom-footer__column {
  flex: 0 0 calc(20% - 20px);
  margin-bottom: 30px;
}

.custom-footer__column-title {
  color: var(--footer-heading-color, #222);
  font-weight: 600;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Heading size variations */
.heading-size--small .custom-footer__column-title {
  font-size: 0.9rem;
}

.heading-size--medium .custom-footer__column-title {
  font-size: 1rem;
}

.heading-size--large .custom-footer__column-title {
  font-size: 1.2rem;
}

.custom-footer__menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.custom-footer__menu-item {
  margin-bottom: 10px;
}

.custom-footer__menu-item a {
  color: var(--footer-link-color, #555);
  text-decoration: none;
  transition: color 0.2s ease;
}

/* Text size variations */
.text-size--small .custom-footer__menu-item a,
.text-size--small .custom-footer__contact p {
  font-size: 0.8rem;
}

.text-size--medium .custom-footer__menu-item a,
.text-size--medium .custom-footer__contact p {
  font-size: 0.9rem;
}

.text-size--large .custom-footer__menu-item a,
.text-size--large .custom-footer__contact p {
  font-size: 1rem;
}

.custom-footer__menu-item a:hover {
  color: var(--footer-link-hover-color, #000);
}

/* Contact Information */
.custom-footer__contact p {
  margin-bottom: 10px;
  font-size: 0.9rem;
  color: var(--footer-text-color, #555);
}

/* Social Section */
.custom-footer__social-section {
  text-align: center;
  margin-bottom: 40px;
}

.custom-footer__social-title {
  color: var(--footer-heading-color, #222);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
}

.custom-footer__social-links {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.custom-footer__social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--social-bg-color, #f0f0f0);
  transition: all 0.3s ease;
}

.custom-footer__social-link svg {
  width: 16px;
  height: 16px;
  fill: var(--social-icon-color, #555);
}

.custom-footer__social-link:hover {
  background-color: var(--social-bg-hover, #e0e0e0);
}

.custom-footer__social-link:hover svg {
  fill: var(--social-icon-hover, #333);
}

/* Footer Bottom */
.custom-footer__bottom {
  background-color: var(--footer-bottom-bg, rgba(0,0,0,0.03));
  padding: 20px 0;
  border-top: 1px solid rgba(0,0,0,0.05);
}

.custom-footer__bottom .custom-footer__container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}

.custom-footer__copyright {
  font-size: 0.85rem;
  color: var(--footer-text-color, #666);
}

.custom-footer__bottom-links {
  display: flex;
  gap: 20px;
}

.custom-footer__bottom-link {
  color: var(--footer-link-color, #555);
  text-decoration: none;
  font-size: 0.85rem;
  transition: color 0.2s ease;
}

.custom-footer__bottom-link:hover {
  color: var(--footer-link-hover-color, #000);
}

/* Payment Icons */
.custom-footer__payment {
  display: flex;
  gap: 8px;
}

.custom-footer__payment-icon {
  width: 38px;
  height: auto;
}

/* Designer Credit */
.custom-footer__designer-credit {
  font-size: 0.85rem;
  color: var(--footer-text-color, #666);
  display: flex;
  align-items: center;
  gap: 5px;
}

.heart-icon {
  margin: 0 3px;
}

/* Responsive Styles */
@media screen and (max-width: 1200px) {
  .custom-footer__column {
    flex: 0 0 calc(25% - 20px);
  }
}

@media screen and (max-width: 991px) {
  .custom-footer__column {
    flex: 0 0 calc(33.333% - 20px);
  }
}

@media screen and (max-width: 767px) {
  .custom-footer {
    padding-top: 40px;
  }
  
  .custom-footer__column {
    flex: 0 0 calc(50% - 15px);
  }
  
  .custom-footer__bottom .custom-footer__container {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .custom-footer__copyright,
  .custom-footer__bottom-links,
  .custom-footer__payment,
  .custom-footer__designer-credit {
    width: 100%;
    justify-content: center;
  }
  
  .custom-footer__bottom-links {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media screen and (max-width: 480px) {
  .custom-footer__column {
    flex: 0 0 100%;
  }
  
  .custom-footer__social-links {
    flex-wrap: wrap;
  }
  
  .custom-footer__bottom-link {
    margin: 5px;
  }
}

/* Mobile accordion styles */
@media screen and (max-width: 767px) {
  .custom-footer__column-title.has-dropdown {
    cursor: pointer;
    position: relative;
    padding-right: 30px; /* Make room for the toggle icon */
  }
  
  .custom-footer__column-title.has-dropdown::after {
    content: '+';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2rem;
    transition: transform 0.3s ease;
    width: 24px;
    height: 24px;
    line-height: 22px;
    text-align: center;
    border-radius: 50%;
    background-color: rgba(0,0,0,0.05);
  }
  
  .custom-footer__column-title.has-dropdown.active::after {
    content: '-';
  }
  
  .custom-footer__menu {
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transition: max-height 0.5s ease, opacity 0.3s ease;
    padding-left: 0;
  }
  
  .custom-footer__column-title.active + .custom-footer__menu {
    max-height: 500px;
    opacity: 1;
    padding-top: 10px;
  }
  
  /* Make sure the social links in the contact column are always visible */
  .custom-footer__contact + .custom-footer__social-title,
  .custom-footer__contact + .custom-footer__social-title + .custom-footer__social-links {
    display: block !important;
    opacity: 1 !important;
    max-height: none !important;
    overflow: visible !important;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const customFooter = document.querySelector('.custom-footer');
  
  if (!customFooter) return;
  
  // Set up mobile accordion functionality
  setupMobileAccordion();
  
  /**
   * Set up accordion functionality for mobile view
   */
  function setupMobileAccordion() {
    // Only apply on mobile screens
    const isMobile = window.innerWidth < 768;
    
    if (isMobile) {
      const columnTitles = customFooter.querySelectorAll('.custom-footer__column-title');
      
      columnTitles.forEach(title => {
        // Skip the column containing contact info
        if (title.nextElementSibling && title.nextElementSibling.classList.contains('custom-footer__contact')) {
          return;
        }
        
        // Add toggle functionality
        title.addEventListener('click', function() {
          const menu = this.nextElementSibling;
          if (!menu || !menu.classList.contains('custom-footer__menu')) return;
          
          // Toggle active class
          this.classList.toggle('active');
          
          // Toggle menu visibility
          if (menu.style.maxHeight) {
            menu.style.maxHeight = null;
            menu.style.opacity = '0';
          } else {
            menu.style.maxHeight = menu.scrollHeight + 'px';
            menu.style.opacity = '1';
          }
        });
        
        // Add "has-dropdown" class for styling
        title.classList.add('has-dropdown');
      });
    }
  }
  
  // Update accordion on window resize
  window.addEventListener('resize', function() {
    if (window.innerWidth >= 768) {
      // Reset all menus on desktop view
      const titles = customFooter.querySelectorAll('.custom-footer__column-title');
      titles.forEach(title => {
        title.classList.remove('active');
      });
    } else {
      // Re-initialize for mobile
      setupMobileAccordion();
    }
  });
});
</script>
