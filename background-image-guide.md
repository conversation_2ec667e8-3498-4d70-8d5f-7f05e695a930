# Custom Footer Background Image Guide

I've enhanced the background image functionality for your custom footer to better match your desired design. Here's how to use it effectively:

## What's Changed?

1. **Direct Background Image Application**:
   - The background image is now applied directly to the footer element
   - It covers the full width of the footer
   - It's positioned at the center bottom to match your design

2. **Customizable Overlay**:
   - Added a semi-transparent overlay between the background image and content
   - The overlay ensures text remains readable while preserving the image
   - You can control both the color and opacity of this overlay

3. **Better Image Quality**:
   - Background image now uses a larger 2000px width version for higher quality
   - Better sizing and positioning for various screen sizes

## How to Configure

In the theme editor, under the Custom Footer section settings, you'll find these options:

1. **Background Image**:
   - Upload an image to use as the footer background
   - Recommended size: 2000 x 800 pixels
   - Choose an image with good contrast for the overlay to work effectively

2. **Background Color/Overlay Color**:
   - If no background image is selected, this will be the solid background color
   - If a background image is used, this color becomes the overlay color

3. **Background Overlay Opacity**:
   - Controls how transparent the overlay is (0-100%)
   - Lower values make the background image more visible
   - Higher values make the overlay more solid for better text readability
   - Default is 85% (good balance for readability)

## Adjustments For Your Design

To match the look in your reference image:

1. **Use a wood-textured background image** like the one in your reference
2. **Set overlay opacity around 50-60%** for a subtle effect
3. **Choose a light beige/cream overlay color** (#f9f8f6 or similar)

## Mobile Considerations

The background image is fully responsive and will adapt to different screen sizes:
- On desktop, more of the image will be visible
- On mobile, the image will be cropped but maintain its center-bottom position

## Troubleshooting

If the background image doesn't look right:

1. **Check image dimensions** - Use a wider image (landscape orientation)
2. **Adjust overlay opacity** - Find the right balance between image visibility and text readability
3. **Try different positioning** - You can manually edit the CSS to change `background-position` if needed

Feel free to experiment with different images and overlay settings to achieve your desired look!
