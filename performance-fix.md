# Performance Fix for Custom Footer JavaScript

I noticed an error in the theme.liquid file related to the JavaScript loading method:

## The Error
```
The script_tag filter is parser-blocking. Use a <script> tag with performance theme-check (ParserBlockingScript)
```

## The Fix

Instead of using Shopify's `script_tag` filter, which creates a parser-blocking script, we should use a standard `<script>` tag with the `defer` attribute for better performance.

### Change from:
```liquid
{{ 'custom-footer.js' | asset_url | script_tag }}
```

### Change to:
```liquid
<script src="{{ 'custom-footer.js' | asset_url }}" defer></script>
```

## What This Means

1. **Parser-blocking** means the browser stops parsing HTML when it encounters the script, which can slow down page loading.

2. The **defer** attribute tells the browser to continue parsing the HTML document while the script downloads, and to execute the script only after the HTML is fully parsed.

3. This change will **improve performance** by allowing your page to load more efficiently while still ensuring the custom footer script is loaded and executed.

## Other Recommendations

1. Make sure your custom-footer.js file doesn't have any code that needs to run immediately during page load. The deferred script will run after the HTML document is parsed.

2. If you're using the all-in-one version of the custom footer (`footer-custom-all-in-one.liquid`), you should also update the inline script tag to use `defer` if possible.

3. The same principle applies to other JavaScript files you might add to your theme in the future.

This change ensures your custom footer JavaScript loads in a performance-friendly way, following Shopify's theme development best practices.
