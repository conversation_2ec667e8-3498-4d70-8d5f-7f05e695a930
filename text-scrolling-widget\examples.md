# Text Scrolling Widget - Usage Examples

## Example 1: Welcome Banner
- Text: "Welcome to Our Store • Premium Quality • Fast Shipping"
- Direction: Left to Right
- Speed: 15 seconds
- Trigger: On Scroll
- Background: Gradient from blue to purple
- Text: White with subtle shadow

## Example 2: Sale Announcement
- Text: "50% OFF SALE • LIMITED TIME • FREE SHIPPING"
- Direction: Right to Left
- Speed: 10 seconds
- Trigger: On Load
- Background: Red gradient
- Text: White with bold weight

## Example 3: Brand Values
- Text: "Sustainable • Ethical • Premium Quality • Handcrafted"
- Direction: Left to Right
- Speed: 25 seconds
- Trigger: On Scroll
- Background: Transparent
- Text: Gradient from green to blue

## JavaScript API Examples

```javascript
// Get widget instance
const widget = document.querySelector('.text-scrolling-widget').textScrollingInstance;

// Control playback
widget.play();
widget.pause();

// Change settings
widget.setSpeed(15);
widget.setDirection('right-to-left');

// Listen to events
widget.widget.addEventListener('textScrolling:start', (e) => {
  console.log('Animation started');
});
```