# Custom Icons Feature Guide

## Overview

The custom footer widget now supports uploading your own custom social media icons. This feature allows you to match your social media icons perfectly with your brand identity instead of using the standard SVG icons.

## Key Features

- **Custom Icon Uploads**: Upload your own PNG or SVG icons for each social network
- **Adjustable Icon Size**: Control the exact size of your icons
- **Multiple Hover Effects**: Choose from several hover animations
- **Independent URLs**: Set custom URLs for each social network
- **Toggle Between Modes**: Easily switch between custom icons and default icons

## Setup Instructions

### 1. Enable Custom Icons

In the theme editor, navigate to the Custom Footer section settings:

1. Find the "Social Links" section
2. Toggle on "Use Custom Icons"

### 2. Upload Icons

For each social network you want to display:

1. Click the "Upload" button next to the social network name
2. Select your icon file (PNG or SVG recommended)
3. Enter the corresponding URL for that social network
4. Repeat for each platform you want to display

### 3. Configure Appearance

Adjust how your icons look and behave:

1. **Icon Size**: Set the size using the slider (16-64px)
2. **Hover Effect**: Choose from:
   - None: No animation
   - Grow: Icon enlarges on hover
   - Fade: Icon partially fades on hover
   - Rotate: Icon slightly rotates on hover

## Recommended Icon Specifications

For best results, prepare your icons according to these specifications:

- **Format**: PNG or SVG (SVG preferred for scaling)
- **Size**: 32 × 32 pixels (base size)
- **Background**: Transparent
- **Style**: Single color works best with hover effects
- **Padding**: Include a small amount of padding around the icon

## Examples

### Upload Examples

| Platform | Recommended Icon |
|----------|------------------|
| Facebook | Square or circular "f" logo |
| Twitter/X | X or bird logo |
| Instagram | Camera or Instagram gradient circle |
| Pinterest | "P" logo or pin icon |
| YouTube | Play button or "YouTube" logo |
| LinkedIn | "in" logo or square LinkedIn icon |

### Hover Effect Examples

- **Grow**: Good for emphasis, makes icons 20% larger on hover
- **Fade**: Subtle effect, reduces opacity to 70% on hover
- **Rotate**: Playful effect, rotates icon by 10 degrees

## Tips for Best Results

1. **Consistent Style**: Use icons with a consistent style and size
2. **High Resolution**: Use 2x size icons for retina displays (64×64px)
3. **Matching Colors**: Choose icon colors that match your theme
4. **Test on Mobile**: Ensure icons are visible and tappable on mobile devices
5. **Accessibility**: Add descriptive alt text in the theme editor

## Troubleshooting

- **Icons Too Small**: Increase the icon size in the theme settings
- **Icons Not Aligned**: Ensure all icons have similar dimensions and padding
- **Hover Effect Not Working**: Make sure your browser supports CSS transitions
- **Icons Not Showing**: Verify the image was uploaded successfully

## Reverting to Default Icons

To switch back to the default social icons:

1. Turn off the "Use Custom Icons" toggle in the theme editor
2. The footer will automatically use the default SVG icons based on your social URLs

---

For additional help or questions about this feature, refer to the main documentation or contact support.