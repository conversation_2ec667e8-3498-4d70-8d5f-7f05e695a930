/* 
  Custom Footer Styles for Outdoor Studio 
  Compatible with Atelier Theme v2.1.6
*/

.custom-footer {
  background-color: var(--footer-bg, #f9f8f6);
  color: var(--footer-color, #333);
  padding: var(--footer-padding-top, 60px) 0 var(--footer-padding-bottom, 0);
  font-family: var(--typebase-font-family);
  position: relative;
  width: 100%;
  border-top: var(--footer-border-width, 1px) solid var(--footer-border-color, #e8e8e8);
}

/* Background image if provided */
.custom-footer {
  background-size: cover;
  background-position: center bottom;
  background-repeat: no-repeat;
  position: relative;
}

.custom-footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(249, 248, 246, 0.85); /* This will be controlled via liquid in the actual implementation */
  z-index: 0;
  pointer-events: none;
}

.custom-footer__container {
  max-width: var(--footer-container-width, 1200px);
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
  width: 100%;
}

.footer-width--full_width .custom-footer__container {
  max-width: none;
  padding: 0 40px;
}

.footer-width--container .custom-footer__container {
  max-width: 1200px;
}

/* Columns layout */
.custom-footer__columns {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 50px;
}

.custom-footer__column {
  flex: 0 0 calc(20% - 20px);
  margin-bottom: 30px;
}

.custom-footer__column-title {
  color: var(--footer-heading-color, #222);
  font-weight: 600;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Heading size variations */
.heading-size--small .custom-footer__column-title {
  font-size: 0.9rem;
}

.heading-size--medium .custom-footer__column-title {
  font-size: 1rem;
}

.heading-size--large .custom-footer__column-title {
  font-size: 1.2rem;
}

.custom-footer__menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.custom-footer__menu-item {
  margin-bottom: 10px;
}

.custom-footer__menu-item a {
  color: var(--footer-link-color, #555);
  text-decoration: none;
  transition: color 0.2s ease;
}

/* Text size variations */
.text-size--small .custom-footer__menu-item a,
.text-size--small .custom-footer__contact p {
  font-size: 0.8rem;
}

.text-size--medium .custom-footer__menu-item a,
.text-size--medium .custom-footer__contact p {
  font-size: 0.9rem;
}

.text-size--large .custom-footer__menu-item a,
.text-size--large .custom-footer__contact p {
  font-size: 1rem;
}

.custom-footer__menu-item a:hover {
  color: var(--footer-link-hover-color, #000);
}

/* Contact Information */
.custom-footer__contact p {
  margin-bottom: 10px;
  font-size: 0.9rem;
  color: var(--footer-text-color, #555);
}

/* Social Section */
.custom-footer__social-section {
  text-align: center;
  margin-bottom: 40px;
}

.custom-footer__social-title {
  color: var(--footer-heading-color, #222);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
}

.custom-footer__social-links {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.custom-footer__social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--social-bg-color, #f0f0f0);
  transition: all 0.3s ease;
}

.custom-footer__social-link svg {
  width: 16px;
  height: 16px;
  fill: var(--social-icon-color, #555);
}

.custom-footer__social-link:hover {
  background-color: var(--social-bg-hover, #e0e0e0);
}

.custom-footer__social-link:hover svg {
  fill: var(--social-icon-hover, #333);
}

/* Footer Bottom */
.custom-footer__bottom {
  background-color: var(--footer-bottom-bg, rgba(0,0,0,0.03));
  padding: 20px 0;
  border-top: var(--footer-border-width, 1px) solid var(--footer-border-color-transparent, rgba(0,0,0,0.05));
}

.custom-footer__bottom .custom-footer__container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}

.custom-footer__copyright {
  font-size: 0.85rem;
  color: var(--footer-text-color, #666);
}

.custom-footer__bottom-links {
  display: flex;
  gap: 20px;
}

.custom-footer__bottom-link {
  color: var(--footer-link-color, #555);
  text-decoration: none;
  font-size: 0.85rem;
  transition: color 0.2s ease;
}

.custom-footer__bottom-link:hover {
  color: var(--footer-link-hover-color, #000);
}

/* Payment Icons */
.custom-footer__payment {
  display: flex;
  gap: 8px;
}

.custom-footer__payment-icon {
  width: 38px;
  height: auto;
}

/* Designer Credit */
.custom-footer__designer-credit {
  font-size: 0.85rem;
  color: var(--footer-text-color, #666);
  display: flex;
  align-items: center;
  gap: 5px;
}

.heart-icon {
  margin: 0 3px;
}

/* Responsive Styles */
@media screen and (max-width: 1200px) {
  .custom-footer__column {
    flex: 0 0 calc(25% - 20px);
  }
}

@media screen and (max-width: 991px) {
  .custom-footer__column {
    flex: 0 0 calc(33.333% - 20px);
  }
}

@media screen and (max-width: 767px) {
  .custom-footer {
    padding-top: 40px;
  }
  
  .custom-footer__column {
    flex: 0 0 calc(50% - 15px);
  }
  
  .custom-footer__bottom .custom-footer__container {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .custom-footer__copyright,
  .custom-footer__bottom-links,
  .custom-footer__payment,
  .custom-footer__designer-credit {
    width: 100%;
    justify-content: center;
  }
  
  .custom-footer__bottom-links {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media screen and (max-width: 480px) {
  .custom-footer__column {
    flex: 0 0 100%;
  }
  
  .custom-footer__social-links {
    flex-wrap: wrap;
  }
  
  .custom-footer__bottom-link {
    margin: 5px;
  }
}
