{% comment %}
  Test Widget - Minimal version to check if sections work
{% endcomment %}

<div style="padding: 50px; text-align: center; background: #f0f0f0; border: 2px solid #333;">
  <h2>🎉 Test Widget is Working!</h2>
  <p>If you can see this, your sections are working correctly.</p>
  
  {% if section.settings.test_text != blank %}
    <p><strong>Your text:</strong> {{ section.settings.test_text }}</p>
  {% endif %}
  
  {% for block in section.blocks %}
    <div style="margin: 20px 0; padding: 20px; background: white; border-radius: 8px;">
      {% if block.settings.block_text != blank %}
        <p>Block {{ forloop.index }}: {{ block.settings.block_text }}</p>
      {% endif %}
    </div>
  {% endfor %}
</div>

{% schema %}
{
  "name": "Test Widget",
  "tag": "section",
  "settings": [
    {
      "type": "text",
      "id": "test_text",
      "label": "Test Text",
      "default": "This is a test!"
    }
  ],
  "blocks": [
    {
      "type": "test_block",
      "name": "Test Block",
      "settings": [
        {
          "type": "text",
          "id": "block_text",
          "label": "Block Text",
          "default": "Test block content"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Test Widget",
      "blocks": [
        {
          "type": "test_block"
        }
      ]
    }
  ]
}
{% endschema %}
