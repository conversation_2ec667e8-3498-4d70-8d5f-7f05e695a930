# Custom Footer Widget Installation Guide

This guide will help you install the custom footer widget in your Shopify store using the Atelier theme v2.1.6.

## Installation Options

You have two installation options:

### Option 1: Standard Installation (Recommended)
This uses separate files for CSS, JS, and the main section.

1. Create the following files:
   - `sections/footer-custom.liquid`
   - `assets/custom-footer.css`
   - `assets/custom-footer.js`
   - Icon snippets (if not already present)

2. Benefits:
   - Cleaner code organization
   - Easier to maintain
   - Better performance (CSS/JS can be cached)

### Option 2: All-in-One Installation
This puts everything in a single file for simpler installation.

1. Create just one file:
   - `sections/footer-custom-all-in-one.liquid`

2. Benefits:
   - Faster installation
   - No dependency management
   - Good for quick implementation

## Step-by-Step Installation Guide

### Standard Installation

1. **Create Footer Section**
   - Go to Shopify Admin → Online Store → Themes
   - Click "Actions" → "Edit code"
   - In the left sidebar, find the "Sections" folder
   - Click "Add a new section"
   - Name it `footer-custom.liquid`
   - Copy the content from the provided `footer-custom.liquid` file

2. **Add CSS File**
   - In the left sidebar, find the "Assets" folder
   - Click "Add a new asset"
   - Name it `custom-footer.css`
   - Copy the content from the provided `custom-footer.css` file

3. **Add JavaScript File**
   - In the "Assets" folder again
   - Click "Add a new asset"
   - Name it `custom-footer.js`
   - Copy the content from the provided `custom-footer.js` file

4. **Add Icon Snippets** (if needed)
   - In the left sidebar, find the "Snippets" folder
   - Add the required social media icon snippets
   - These should be named like `icon-facebook.liquid`, etc.

### All-in-One Installation

1. **Create All-in-One Section**
   - Go to Shopify Admin → Online Store → Themes
   - Click "Actions" → "Edit code"
   - In the left sidebar, find the "Sections" folder
   - Click "Add a new section"
   - Name it `footer-custom-all-in-one.liquid`
   - Copy the content from the provided `footer-custom-all-in-one.liquid` file

## Implementing in Your Theme

1. **Add to Theme Layout**
   - Go to Shopify Admin → Online Store → Themes
   - Click "Customize"
   - Navigate to the footer area
   - Click "Add section"
   - Select "Custom Footer"

2. **Configure Settings**
   - While still in the theme editor:
   - Click on the Custom Footer section
   - Configure all settings in the sidebar:
     - Layout settings
     - Typography settings
     - Color settings
     - Border styling
     - Background options

3. **Save Changes**
   - Click "Save" in the top-right corner

## Troubleshooting

If the footer doesn't appear correctly:

1. **Check Console for Errors**
   - Open browser developer tools (F12)
   - Check for any JavaScript errors

2. **Verify CSS Loading**
   - Check if custom-footer.css is loading properly
   - Ensure no CSS conflicts with other theme elements

3. **Mobile Issues**
   - Test on multiple device sizes
   - Ensure JavaScript is working for mobile accordions

4. **Background Image Not Displaying**
   - Verify image URL is correct
   - Check image dimensions (recommend 2000px wide minimum)
   - Ensure overlay settings aren't hiding the image

## Maintenance

To update your custom footer in the future:

1. **Backup First**
   - Always download a backup of your theme before making changes

2. **Edit Code**
   - Follow the same steps as installation, but edit existing files
   - Test thoroughly after any changes

For additional help, refer to the customization guide or contact me directly.
