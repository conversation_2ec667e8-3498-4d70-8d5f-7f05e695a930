{% comment %}
  Image Slider Widget for Atelier Theme v2.1.6 - FIXED VERSION
  All syntax errors resolved and modern Shopify filters used
{% endcomment %}

<div class="image-slider-widget" 
     id="image-slider-{{ section.id }}"
     data-slider-speed="{{ section.settings.slider_speed | default: 5000 }}"
     data-auto-play="{{ section.settings.auto_play | default: true }}"
     data-show-navigation="{{ section.settings.show_navigation | default: true }}"
     data-show-pagination="{{ section.settings.show_pagination | default: true }}"
     data-slides-per-view="{{ section.settings.slides_per_view | default: 1 }}"
     data-slides-per-view-mobile="{{ section.settings.slides_per_view_mobile | default: 1 }}">
  
  <div class="image-slider-container">
    {% if section.settings.show_navigation %}
      <button class="image-slider-nav image-slider-nav--prev" aria-label="Previous slide">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    {% endif %}
    
    <div class="image-slider-track-container">
      <div class="image-slider-track">
        {% for block in section.blocks %}
          <div class="image-slider-slide" 
               data-block-id="{{ block.id }}"
               {{ block.shopify_attributes }}>
            {% if block.settings.slide_image %}
              <div class="image-slider-image-container">
                {% if block.settings.slide_link != blank %}
                  <a href="{{ block.settings.slide_link }}" class="image-slider-link">
                {% endif %}
                
                {% assign slide_alt = block.settings.slide_alt | default: block.settings.slide_title | default: 'Slide image' %}
                <img src="{{ block.settings.slide_image | image_url: width: 800, height: 600 }}"
                     srcset="{{ block.settings.slide_image | image_url: width: 400, height: 300 }} 400w,
                             {{ block.settings.slide_image | image_url: width: 800, height: 600 }} 800w,
                             {{ block.settings.slide_image | image_url: width: 1200, height: 900 }} 1200w"
                     sizes="(max-width: 768px) 400px, (max-width: 1200px) 800px, 1200px"
                     alt="{{ slide_alt }}"
                     class="image-slider-image"
                     width="800"
                     height="600"
                     loading="lazy">
                
                {% if block.settings.slide_title != blank or block.settings.slide_description != blank %}
                  <div class="image-slider-content">
                    {% if block.settings.slide_title != blank %}
                      <h3 class="image-slider-title">{{ block.settings.slide_title }}</h3>
                    {% endif %}
                    {% if block.settings.slide_description != blank %}
                      <p class="image-slider-description">{{ block.settings.slide_description }}</p>
                    {% endif %}
                  </div>
                {% endif %}
                
                {% if block.settings.slide_link != blank %}
                  </a>
                {% endif %}
              </div>
            {% endif %}
          </div>
        {% endfor %}
      </div>
    </div>
    
    {% if section.settings.show_navigation %}
      <button class="image-slider-nav image-slider-nav--next" aria-label="Next slide">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    {% endif %}
  </div>
  
  {% if section.settings.show_pagination %}
    <div class="image-slider-pagination">
      {% for block in section.blocks %}
        <button class="image-slider-pagination-dot" 
                data-slide-index="{{ forloop.index0 }}"
                aria-label="Go to slide {{ forloop.index }}"></button>
      {% endfor %}
    </div>
  {% endif %}
</div>

<style>
  #image-slider-{{ section.id }} {
    --slider-height: {{ section.settings.slider_height | default: 400 }}px;
    --slider-height-mobile: {{ section.settings.slider_height_mobile | default: 300 }}px;
    --border-radius: {{ section.settings.image_border_radius | default: 12 }}px;
    --gap-between-slides: {{ section.settings.gap_between_slides | default: 20 }}px;
    --nav-color: {{ section.settings.navigation_color | default: '#333333' }};
    --nav-bg: {{ section.settings.navigation_background | default: 'rgba(255,255,255,0.9)' }};
    --pagination-color: {{ section.settings.pagination_color | default: 'rgba(0,0,0,0.3)' }};
    --pagination-active-color: {{ section.settings.pagination_active_color | default: '#333333' }};
    --overlay-opacity: {{ section.settings.content_overlay_opacity | default: 70 | divided_by: 100.0 }};
    --content-text-color: {{ section.settings.content_text_color | default: '#ffffff' }};
    --section-padding-top: {{ section.settings.section_padding_top | default: 60 }}px;
    --section-padding-bottom: {{ section.settings.section_padding_bottom | default: 60 }}px;
    --section-bg: {{ section.settings.section_background_color | default: '#ffffff' }};
  }

  /* Main Widget Styles */
  .image-slider-widget {
    width: 100%;
    background: var(--section-bg);
    padding: var(--section-padding-top) 0 var(--section-padding-bottom);
    position: relative;
    overflow: hidden;
  }

  .image-slider-container {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .image-slider-track-container {
    position: relative;
    height: var(--slider-height);
    overflow: hidden;
    border-radius: var(--border-radius);
  }

  .image-slider-track {
    display: flex;
    height: 100%;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform;
    backface-visibility: hidden;
  }

  .image-slider-slide {
    flex: 0 0 auto;
    height: 100%;
    margin-right: var(--gap-between-slides);
    position: relative;
  }

  .image-slider-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: var(--border-radius);
  }

  .image-slider-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    backface-visibility: hidden;
  }

  .image-slider-link:hover .image-slider-image {
    transform: scale(1.05);
  }

  /* Content Overlay */
  .image-slider-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0, var(--overlay-opacity)));
    color: var(--content-text-color);
    padding: 40px 30px 30px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
  }

  .image-slider-slide:hover .image-slider-content {
    transform: translateY(0);
  }

  .image-slider-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 10px 0;
    line-height: 1.2;
  }

  .image-slider-description {
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.9;
    line-height: 1.4;
  }

  /* Navigation Arrows */
  .image-slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: var(--nav-bg);
    color: var(--nav-color);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    backdrop-filter: blur(10px);
  }

  .image-slider-nav:hover {
    background: var(--nav-bg);
    transform: translateY(-50%) scale(1.1);
  }

  .image-slider-nav--prev {
    left: 20px;
  }

  .image-slider-nav--next {
    right: 20px;
  }

  /* Pagination Dots */
  .image-slider-pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
  }

  .image-slider-pagination-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: var(--pagination-color);
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .image-slider-pagination-dot.active {
    background: var(--pagination-active-color);
    transform: scale(1.2);
  }

  /* Mobile Responsive */
  @media screen and (max-width: 768px) {
    .image-slider-track-container {
      height: var(--slider-height-mobile);
    }
    
    .image-slider-container {
      padding: 0 15px;
    }
    
    .image-slider-nav {
      width: 40px;
      height: 40px;
    }
    
    .image-slider-nav--prev {
      left: 10px;
    }
    
    .image-slider-nav--next {
      right: 10px;
    }
    
    .image-slider-content {
      padding: 20px 15px 15px;
    }
    
    .image-slider-title {
      font-size: 1.2rem;
    }
    
    .image-slider-description {
      font-size: 0.8rem;
    }
  }

  /* Accessibility */
  @media (prefers-reduced-motion: reduce) {
    .image-slider-track {
      transition: none;
    }
    
    .image-slider-image {
      transition: none;
    }
    
    .image-slider-content {
      transition: none;
      transform: translateY(0);
    }
  }

  /* Focus States */
  .image-slider-nav:focus,
  .image-slider-pagination-dot:focus {
    outline: 2px solid var(--nav-color);
    outline-offset: 2px;
  }

  /* Hide navigation when only one slide */
  .image-slider-widget[data-slides-count="1"] .image-slider-nav,
  .image-slider-widget[data-slides-count="1"] .image-slider-pagination {
    display: none;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    class ImageSliderWidget {
      constructor(element) {
        this.widget = element;
        this.container = element.querySelector('.image-slider-container');
        this.track = element.querySelector('.image-slider-track');
        this.slides = element.querySelectorAll('.image-slider-slide');
        this.prevBtn = element.querySelector('.image-slider-nav--prev');
        this.nextBtn = element.querySelector('.image-slider-nav--next');
        this.pagination = element.querySelector('.image-slider-pagination');
        this.paginationDots = element.querySelectorAll('.image-slider-pagination-dot');

        // Settings from data attributes with fallbacks
        this.autoPlay = element.dataset.autoPlay === 'true';
        this.sliderSpeed = parseInt(element.dataset.sliderSpeed) || 5000;
        this.slidesPerView = parseInt(element.dataset.slidesPerView) || 1;
        this.slidesPerViewMobile = parseInt(element.dataset.slidesPerViewMobile) || 1;

        // State
        this.currentSlide = 0;
        this.totalSlides = this.slides.length;
        this.isAnimating = false;
        this.autoPlayInterval = null;
        this.touchStartX = 0;
        this.touchEndX = 0;

        this.init();
      }

      init() {
        if (this.totalSlides === 0) return;

        // Set slides count attribute
        this.widget.setAttribute('data-slides-count', this.totalSlides);

        this.setupSlides();
        this.setupEventListeners();
        this.updatePagination();

        if (this.autoPlay && this.totalSlides > 1) {
          this.startAutoPlay();
        }
      }

      setupSlides() {
        const currentSlidesPerView = window.innerWidth <= 768 ? this.slidesPerViewMobile : this.slidesPerView;
        const slideWidth = `calc((100% - (var(--gap-between-slides) * ${currentSlidesPerView - 1})) / ${currentSlidesPerView})`;

        this.slides.forEach(slide => {
          slide.style.width = slideWidth;
        });

        this.updateSlidePosition();
      }

      setupEventListeners() {
        // Navigation buttons
        if (this.prevBtn) {
          this.prevBtn.addEventListener('click', () => this.prevSlide());
        }

        if (this.nextBtn) {
          this.nextBtn.addEventListener('click', () => this.nextSlide());
        }

        // Pagination dots
        this.paginationDots.forEach((dot, index) => {
          dot.addEventListener('click', () => this.goToSlide(index));
        });

        // Touch events for mobile swipe
        this.track.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: true });
        this.track.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: true });

        // Pause auto-play on hover
        this.widget.addEventListener('mouseenter', () => this.pauseAutoPlay());
        this.widget.addEventListener('mouseleave', () => this.resumeAutoPlay());

        // Handle window resize
        window.addEventListener('resize', this.debounce(() => this.setupSlides(), 250));

        // Handle visibility change
        document.addEventListener('visibilitychange', () => {
          if (document.hidden) {
            this.pauseAutoPlay();
          } else if (this.autoPlay) {
            this.resumeAutoPlay();
          }
        });
      }

      updateSlidePosition() {
        if (this.isAnimating) return;

        const currentSlidesPerView = window.innerWidth <= 768 ? this.slidesPerViewMobile : this.slidesPerView;
        const slideWidth = 100 / currentSlidesPerView;
        const translateX = -(this.currentSlide * slideWidth);

        this.track.style.transform = `translateX(${translateX}%)`;
        this.updatePagination();
      }

      updatePagination() {
        this.paginationDots.forEach((dot, index) => {
          dot.classList.toggle('active', index === this.currentSlide);
        });
      }

      nextSlide() {
        if (this.isAnimating || this.totalSlides <= 1) return;

        const currentSlidesPerView = window.innerWidth <= 768 ? this.slidesPerViewMobile : this.slidesPerView;
        const maxSlide = Math.max(0, this.totalSlides - currentSlidesPerView);

        this.currentSlide = this.currentSlide >= maxSlide ? 0 : this.currentSlide + 1;
        this.animateToSlide();
      }

      prevSlide() {
        if (this.isAnimating || this.totalSlides <= 1) return;

        const currentSlidesPerView = window.innerWidth <= 768 ? this.slidesPerViewMobile : this.slidesPerView;
        const maxSlide = Math.max(0, this.totalSlides - currentSlidesPerView);

        this.currentSlide = this.currentSlide <= 0 ? maxSlide : this.currentSlide - 1;
        this.animateToSlide();
      }

      goToSlide(index) {
        if (this.isAnimating || index === this.currentSlide || this.totalSlides <= 1) return;

        this.currentSlide = Math.max(0, Math.min(index, this.totalSlides - 1));
        this.animateToSlide();
      }

      animateToSlide() {
        this.isAnimating = true;
        this.updateSlidePosition();

        setTimeout(() => {
          this.isAnimating = false;
        }, 500);
      }

      handleTouchStart(e) {
        this.touchStartX = e.touches[0].clientX;
      }

      handleTouchEnd(e) {
        this.touchEndX = e.changedTouches[0].clientX;
        this.handleSwipe();
      }

      handleSwipe() {
        const swipeThreshold = 50;
        const swipeDistance = this.touchStartX - this.touchEndX;

        if (Math.abs(swipeDistance) > swipeThreshold) {
          if (swipeDistance > 0) {
            this.nextSlide();
          } else {
            this.prevSlide();
          }
        }
      }

      startAutoPlay() {
        if (!this.autoPlay || this.autoPlayInterval || this.totalSlides <= 1) return;

        this.autoPlayInterval = setInterval(() => {
          this.nextSlide();
        }, this.sliderSpeed);
      }

      pauseAutoPlay() {
        if (this.autoPlayInterval) {
          clearInterval(this.autoPlayInterval);
          this.autoPlayInterval = null;
        }
      }

      resumeAutoPlay() {
        if (this.autoPlay && !this.autoPlayInterval && this.totalSlides > 1) {
          this.startAutoPlay();
        }
      }

      debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
          const later = () => {
            clearTimeout(timeout);
            func(...args);
          };
          clearTimeout(timeout);
          timeout = setTimeout(later, wait);
        };
      }

      destroy() {
        this.pauseAutoPlay();
      }
    }

    // Initialize all sliders
    const sliders = document.querySelectorAll('.image-slider-widget');
    sliders.forEach(slider => {
      const instance = new ImageSliderWidget(slider);
      slider.imageSliderInstance = instance;
    });
  });

  // Handle Shopify section reloads
  document.addEventListener('shopify:section:load', function(event) {
    const sliders = event.target.querySelectorAll('.image-slider-widget');
    sliders.forEach(slider => {
      if (!slider.imageSliderInstance) {
        const instance = new ImageSliderWidget(slider);
        slider.imageSliderInstance = instance;
      }
    });
  });

  document.addEventListener('shopify:section:unload', function(event) {
    const sliders = event.target.querySelectorAll('.image-slider-widget');
    sliders.forEach(slider => {
      if (slider.imageSliderInstance) {
        slider.imageSliderInstance.destroy();
        slider.imageSliderInstance = null;
      }
    });
  });
</script>

{% schema %}
{
  "name": "Image Slider Widget",
  "tag": "section",
  "class": "image-slider-section",
  "settings": [
    {
      "type": "header",
      "content": "Layout Settings"
    },
    {
      "type": "range",
      "id": "slider_height",
      "min": 200,
      "max": 800,
      "step": 20,
      "unit": "px",
      "label": "Slider Height (Desktop)",
      "default": 400
    },
    {
      "type": "range",
      "id": "slider_height_mobile",
      "min": 150,
      "max": 500,
      "step": 20,
      "unit": "px",
      "label": "Slider Height (Mobile)",
      "default": 300
    },
    {
      "type": "range",
      "id": "slides_per_view",
      "min": 1,
      "max": 4,
      "step": 1,
      "label": "Slides Per View (Desktop)",
      "default": 1
    },
    {
      "type": "range",
      "id": "slides_per_view_mobile",
      "min": 1,
      "max": 2,
      "step": 1,
      "label": "Slides Per View (Mobile)",
      "default": 1
    },
    {
      "type": "range",
      "id": "gap_between_slides",
      "min": 0,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Gap Between Slides",
      "default": 20
    },
    {
      "type": "range",
      "id": "section_padding_top",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "label": "Top Padding",
      "default": 60
    },
    {
      "type": "range",
      "id": "section_padding_bottom",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "label": "Bottom Padding",
      "default": 60
    },
    {
      "type": "header",
      "content": "Slider Behavior"
    },
    {
      "type": "checkbox",
      "id": "auto_play",
      "label": "Auto Play",
      "default": true
    },
    {
      "type": "range",
      "id": "slider_speed",
      "min": 2000,
      "max": 10000,
      "step": 500,
      "unit": "ms",
      "label": "Auto Play Speed",
      "default": 5000
    },
    {
      "type": "checkbox",
      "id": "show_navigation",
      "label": "Show Navigation Arrows",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_pagination",
      "label": "Show Pagination Dots",
      "default": true
    },
    {
      "type": "header",
      "content": "Image Styling"
    },
    {
      "type": "range",
      "id": "image_border_radius",
      "min": 0,
      "max": 50,
      "step": 2,
      "unit": "px",
      "label": "Image Border Radius",
      "default": 12
    },
    {
      "type": "range",
      "id": "content_overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "%",
      "label": "Content Overlay Opacity",
      "default": 70
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "section_background_color",
      "label": "Section Background",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "navigation_color",
      "label": "Navigation Arrow Color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "navigation_background",
      "label": "Navigation Background",
      "default": "rgba(255,255,255,0.9)"
    },
    {
      "type": "color",
      "id": "pagination_color",
      "label": "Pagination Dot Color",
      "default": "rgba(0,0,0,0.3)"
    },
    {
      "type": "color",
      "id": "pagination_active_color",
      "label": "Active Pagination Color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "content_text_color",
      "label": "Content Text Color",
      "default": "#ffffff"
    }
  ],
  "blocks": [
    {
      "type": "slide",
      "name": "Slide",
      "settings": [
        {
          "type": "image_picker",
          "id": "slide_image",
          "label": "Slide Image"
        },
        {
          "type": "text",
          "id": "slide_alt",
          "label": "Image Alt Text",
          "info": "Describe the image for accessibility"
        },
        {
          "type": "text",
          "id": "slide_title",
          "label": "Slide Title"
        },
        {
          "type": "textarea",
          "id": "slide_description",
          "label": "Slide Description"
        },
        {
          "type": "url",
          "id": "slide_link",
          "label": "Slide Link",
          "info": "Optional link when slide is clicked"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Image Slider Widget",
      "blocks": [
        {
          "type": "slide",
          "settings": {
            "slide_title": "Beautiful Outdoor Furniture",
            "slide_description": "Transform your outdoor space with our premium furniture collection"
          }
        },
        {
          "type": "slide",
          "settings": {
            "slide_title": "Quality Craftsmanship",
            "slide_description": "Each piece is carefully crafted with attention to detail and durability"
          }
        },
        {
          "type": "slide",
          "settings": {
            "slide_title": "Perfect for Any Space",
            "slide_description": "From small patios to large gardens, find the perfect fit for your outdoor area"
          }
        }
      ]
    }
  ]
}
{% endschema %}
